# Start services
./start-services.sh

# Test APIs
curl http://localhost:3000/courses
curl http://localhost:3001/campaigns

```
Tôi muốn xây dựng một hệ thống có 2 services:
Order Service (chỉ cần mua khóa học đơn giản - kh<PERSON><PERSON> học không có kho hàng, không có bảng users cũng được chỉ dùng user_id)
Campaign Service: trong một campaign tung ra một số điểm nhất định và thực hiện tặng điểm cho user; đảm bảo việc mua hàng phải đúng thứ tự để người mua trước phải được nhận điểm trước.

Công nghệ sử dụng Kafka, NestJS
```
```
I've completed the implementation of both services. Here's a summary of how the system works:

Order Service (Port 3000):
Exposes endpoints for course listing and order creation
Publishes order events to Kafka topic 'order.created'
Manages basic course catalog and order processing
Campaign Service (Port 3001):
Manages point campaigns with total points and date ranges
Listens to 'order.created' events from Kafka
Distributes points to users based on their order timestamps
Ensures idempotency (no duplicate point awards)
Tracks campaign status and remaining points
To test the system, you'll need:

A running Kafka instance on localhost:9092
Both services running simultaneously
You can test the flow by:

Creating a campaign using POST /campaigns on port 3001
Creating an order using POST /orders on port 3000
Checking awarded points using GET /points/{userId} on port 3001
```
```
Now our system architecture is complete with three services:

API Gateway (Port 3002):
Acts as a single entry point for all client requests
Routes requests to appropriate services
Handles CORS and basic request validation
Order Service (Port 3000):
Manages course catalog and orders
Publishes order events to Kafka
Campaign Service (Port 3001):
Manages campaigns and point distribution
Consumes order events from Kafka
Ensures ordered point distribution
The flow when a user places an order will be:

Client sends POST request to API Gateway at /orders
API Gateway forwards request to Order Service
Order Service creates the order and publishes to Kafka
Campaign Service consumes the order event and awards points if applicable
Points can be checked via API Gateway at /points/{userId}
```
