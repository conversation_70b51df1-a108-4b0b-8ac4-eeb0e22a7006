#!/bin/bash

echo "🚀 Starting SQLite-powered microservices..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting Order Service on port 3000...${NC}"
cd order-service
npm run start:dev &
ORDER_PID=$!
echo -e "${GREEN}Order Service PID: $ORDER_PID${NC}"

echo -e "${YELLOW}Starting Campaign Service on port 3001...${NC}"
cd ../campaign-service
npm run start:dev &
CAMPAIGN_PID=$!
echo -e "${GREEN}Campaign Service PID: $CAMPAIGN_PID${NC}"

echo ""
echo -e "${GREEN}✅ Services started successfully!${NC}"
echo ""
echo "📊 Available endpoints:"
echo "  Order Service (http://localhost:3000):"
echo "    GET  /courses"
echo "    POST /orders"
echo "    GET  /orders"
echo ""
echo "  Campaign Service (http://localhost:3001):"
echo "    POST /campaigns"
echo "    GET  /campaigns"
echo "    GET  /points/:userId"
echo ""
echo "🗄️ SQLite databases:"
echo "  order-service/order.db"
echo "  campaign-service/campaign.db"
echo ""
echo "To stop services, run: kill $ORDER_PID $CAMPAIGN_PID"
echo ""
echo "Press Ctrl+C to stop this script (services will continue running)"

# Keep script running
wait
