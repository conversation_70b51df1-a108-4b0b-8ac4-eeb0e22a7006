#!/bin/bash

echo "=== Testing SQLite Migration ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ $2${NC}"
    else
        echo -e "${RED}✗ $2${NC}"
    fi
}

echo -e "${YELLOW}Starting services...${NC}"

# Start Order Service in background
cd order-service
npm run start:dev &
ORDER_PID=$!
echo "Order Service PID: $ORDER_PID"

# Wait a bit for order service to start
sleep 5

# Start Campaign Service in background  
cd ../campaign-service
npm run start:dev &
CAMPAIGN_PID=$!
echo "Campaign Service PID: $CAMPAIGN_PID"

# Wait for services to fully start
echo "Waiting for services to start..."
sleep 10

echo -e "${YELLOW}Testing Order Service...${NC}"

# Test get courses
echo "Testing GET /courses..."
COURSES_RESPONSE=$(curl -s -w "%{http_code}" http://localhost:3000/courses)
HTTP_CODE="${COURSES_RESPONSE: -3}"
if [ "$HTTP_CODE" = "200" ]; then
    print_status 0 "GET /courses"
    echo "Response: ${COURSES_RESPONSE%???}"
else
    print_status 1 "GET /courses (HTTP $HTTP_CODE)"
fi

echo ""

# Test create order
echo "Testing POST /orders..."
ORDER_RESPONSE=$(curl -s -w "%{http_code}" -X POST http://localhost:3000/orders \
  -H "Content-Type: application/json" \
  -d '{"userId": "user123", "courseId": "1"}')
HTTP_CODE="${ORDER_RESPONSE: -3}"
if [ "$HTTP_CODE" = "201" ]; then
    print_status 0 "POST /orders"
    echo "Response: ${ORDER_RESPONSE%???}"
else
    print_status 1 "POST /orders (HTTP $HTTP_CODE)"
fi

echo ""

# Test get orders
echo "Testing GET /orders..."
ORDERS_RESPONSE=$(curl -s -w "%{http_code}" http://localhost:3000/orders)
HTTP_CODE="${ORDERS_RESPONSE: -3}"
if [ "$HTTP_CODE" = "200" ]; then
    print_status 0 "GET /orders"
    echo "Response: ${ORDERS_RESPONSE%???}"
else
    print_status 1 "GET /orders (HTTP $HTTP_CODE)"
fi

echo ""
echo -e "${YELLOW}Testing Campaign Service...${NC}"

# Test create campaign
echo "Testing POST /campaigns..."
CAMPAIGN_RESPONSE=$(curl -s -w "%{http_code}" -X POST http://localhost:3001/campaigns \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Campaign",
    "totalPoints": 1000,
    "startDate": "2025-01-01T00:00:00.000Z",
    "endDate": "2025-12-31T23:59:59.000Z"
  }')
HTTP_CODE="${CAMPAIGN_RESPONSE: -3}"
if [ "$HTTP_CODE" = "201" ]; then
    print_status 0 "POST /campaigns"
    echo "Response: ${CAMPAIGN_RESPONSE%???}"
else
    print_status 1 "POST /campaigns (HTTP $HTTP_CODE)"
fi

echo ""

# Test get campaigns
echo "Testing GET /campaigns..."
CAMPAIGNS_RESPONSE=$(curl -s -w "%{http_code}" http://localhost:3001/campaigns)
HTTP_CODE="${CAMPAIGNS_RESPONSE: -3}"
if [ "$HTTP_CODE" = "200" ]; then
    print_status 0 "GET /campaigns"
    echo "Response: ${CAMPAIGNS_RESPONSE%???}"
else
    print_status 1 "GET /campaigns (HTTP $HTTP_CODE)"
fi

echo ""

# Wait a bit for Kafka message processing
echo "Waiting for Kafka message processing..."
sleep 3

# Test get user points
echo "Testing GET /points/user123..."
POINTS_RESPONSE=$(curl -s -w "%{http_code}" http://localhost:3001/points/user123)
HTTP_CODE="${POINTS_RESPONSE: -3}"
if [ "$HTTP_CODE" = "200" ]; then
    print_status 0 "GET /points/user123"
    echo "Response: ${POINTS_RESPONSE%???}"
else
    print_status 1 "GET /points/user123 (HTTP $HTTP_CODE)"
fi

echo ""
echo -e "${YELLOW}Checking database files...${NC}"

# Check if SQLite databases were created
if [ -f "order-service/order.db" ]; then
    print_status 0 "Order SQLite database created"
else
    print_status 1 "Order SQLite database not found"
fi

if [ -f "campaign-service/campaign.db" ]; then
    print_status 0 "Campaign SQLite database created"
else
    print_status 1 "Campaign SQLite database not found"
fi

echo ""
echo -e "${YELLOW}Cleaning up...${NC}"

# Kill the services
kill $ORDER_PID 2>/dev/null
kill $CAMPAIGN_PID 2>/dev/null

echo -e "${GREEN}Testing completed!${NC}"
