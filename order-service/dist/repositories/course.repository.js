"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const course_entity_1 = require("../entities/course.entity");
let CourseRepository = class CourseRepository {
    constructor(courseRepo) {
        this.courseRepo = courseRepo;
    }
    async create(courseData) {
        const course = this.courseRepo.create(courseData);
        return await this.courseRepo.save(course);
    }
    async findAll() {
        return await this.courseRepo.find();
    }
    async findById(id) {
        return await this.courseRepo.findOne({ where: { id } });
    }
    async update(id, updateData) {
        await this.courseRepo.update(id, updateData);
        return await this.findById(id);
    }
    async delete(id) {
        await this.courseRepo.delete(id);
    }
    async seedDefaultCourses() {
        const existingCourses = await this.findAll();
        if (existingCourses.length === 0) {
            const defaultCourses = [
                { id: '1', name: 'NestJS Course', price: 100 },
                { id: '2', name: 'Kafka Course', price: 150 },
            ];
            for (const courseData of defaultCourses) {
                await this.create(courseData);
            }
        }
    }
};
exports.CourseRepository = CourseRepository;
exports.CourseRepository = CourseRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(course_entity_1.Course)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CourseRepository);
//# sourceMappingURL=course.repository.js.map