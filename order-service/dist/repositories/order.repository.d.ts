import { Repository } from 'typeorm';
import { Order } from '../entities/order.entity';
export declare class OrderRepository {
    private readonly orderRepo;
    constructor(orderRepo: Repository<Order>);
    create(orderData: Partial<Order>): Promise<Order>;
    findAll(): Promise<Order[]>;
    findById(id: string): Promise<Order | null>;
    findByUserId(userId: string): Promise<Order[]>;
    update(id: string, updateData: Partial<Order>): Promise<Order | null>;
    delete(id: string): Promise<void>;
}
