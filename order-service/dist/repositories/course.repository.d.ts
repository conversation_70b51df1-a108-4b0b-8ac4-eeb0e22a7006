import { Repository } from 'typeorm';
import { Course } from '../entities/course.entity';
export declare class CourseRepository {
    private readonly courseRepo;
    constructor(courseRepo: Repository<Course>);
    create(courseData: Partial<Course>): Promise<Course>;
    findAll(): Promise<Course[]>;
    findById(id: string): Promise<Course | null>;
    update(id: string, updateData: Partial<Course>): Promise<Course | null>;
    delete(id: string): Promise<void>;
    seedDefaultCourses(): Promise<void>;
}
