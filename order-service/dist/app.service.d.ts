import { OnModuleInit } from '@nestjs/common';
import { ClientKafka } from '@nestjs/microservices';
import { Course } from './entities/course.entity';
import { Order } from './entities/order.entity';
import { CourseRepository, OrderRepository } from './repositories';
export declare class AppService implements OnModuleInit {
    private readonly kafkaClient;
    private readonly courseRepository;
    private readonly orderRepository;
    constructor(kafkaClient: ClientKafka, courseRepository: CourseRepository, orderRepository: OrderRepository);
    onModuleInit(): Promise<void>;
    createOrder(userId: string, courseId: string): Promise<Order>;
    getOrders(): Promise<Order[]>;
    getCourses(): Promise<Course[]>;
}
