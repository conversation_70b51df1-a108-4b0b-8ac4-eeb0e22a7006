import { AppService } from './app.service';
import { Course } from './entities/course.entity';
import { Order } from './entities/order.entity';
export declare class AppController {
    private readonly appService;
    constructor(appService: AppService);
    createOrder(createOrderDto: {
        userId: string;
        courseId: string;
    }): Promise<Order>;
    getOrders(): Promise<Order[]>;
    getCourses(): Promise<Course[]>;
}
