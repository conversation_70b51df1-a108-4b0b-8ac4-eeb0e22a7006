"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppService = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const uuid_1 = require("uuid");
const repositories_1 = require("./repositories");
let AppService = class AppService {
    constructor(kafkaClient, courseRepository, orderRepository) {
        this.kafkaClient = kafkaClient;
        this.courseRepository = courseRepository;
        this.orderRepository = orderRepository;
    }
    async onModuleInit() {
        await this.courseRepository.seedDefaultCourses();
    }
    async createOrder(userId, courseId) {
        const course = await this.courseRepository.findById(courseId);
        if (!course) {
            throw new Error('Course not found');
        }
        const orderData = {
            id: (0, uuid_1.v4)(),
            userId,
            courseId,
            status: 'pending',
            createdAt: new Date(),
        };
        const order = await this.orderRepository.create(orderData);
        return order;
    }
    async getOrders() {
        return await this.orderRepository.findAll();
    }
    async getCourses() {
        return await this.courseRepository.findAll();
    }
};
exports.AppService = AppService;
exports.AppService = AppService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('KAFKA_SERVICE')),
    __metadata("design:paramtypes", [microservices_1.ClientKafka,
        repositories_1.CourseRepository,
        repositories_1.OrderRepository])
], AppService);
//# sourceMappingURL=app.service.js.map