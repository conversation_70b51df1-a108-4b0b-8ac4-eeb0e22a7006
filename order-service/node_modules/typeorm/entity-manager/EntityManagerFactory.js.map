{"version": 3, "sources": ["../../src/entity-manager/EntityManagerFactory.ts"], "names": [], "mappings": ";;;AACA,mDAA+C;AAC/C,6DAAyD;AACzD,6DAAyD;AAGzD;;GAEG;AACH,MAAa,oBAAoB;IAC7B;;OAEG;IACH,MAAM,CAAC,UAAsB,EAAE,WAAyB;QACpD,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;YAC5C,OAAO,IAAI,uCAAkB,CAAC,UAAU,CAAC,CAAA;QAE7C,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;YAC1C,OAAO,IAAI,uCAAkB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;QAE1D,OAAO,IAAI,6BAAa,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;CACJ;AAbD,oDAaC", "file": "EntityManagerFactory.js", "sourcesContent": ["import { DataSource } from \"../data-source/DataSource\"\nimport { EntityManager } from \"./EntityManager\"\nimport { MongoEntityManager } from \"./MongoEntityManager\"\nimport { SqljsEntityManager } from \"./SqljsEntityManager\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\n\n/**\n * Helps to create entity managers.\n */\nexport class EntityManagerFactory {\n    /**\n     * Creates a new entity manager depend on a given connection's driver.\n     */\n    create(connection: DataSource, queryRunner?: QueryRunner): EntityManager {\n        if (connection.driver.options.type === \"mongodb\")\n            return new MongoEntityManager(connection)\n\n        if (connection.driver.options.type === \"sqljs\")\n            return new SqljsEntityManager(connection, queryRunner)\n\n        return new EntityManager(connection, queryRunner)\n    }\n}\n"], "sourceRoot": ".."}