{"version": 3, "sources": ["../../src/commands/MigrationCreateCommand.ts"], "names": [], "mappings": ";;;;AAAA,0DAAwB;AACxB,wDAAuB;AAEvB,6DAAyD;AACzD,qDAA+C;AAC/C,iDAA6C;AAE7C;;GAEG;AACH,MAAa,sBAAsB;IAAnC;QACI,YAAO,GAAG,yBAAyB,CAAA;QACnC,aAAQ,GAAG,+BAA+B,CAAA;IAoH9C,CAAC;IAlHG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI;aACN,UAAU,CAAC,MAAM,EAAE;YAChB,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,4BAA4B;YACtC,YAAY,EAAE,IAAI;SACrB,CAAC;aACD,MAAM,CAAC,GAAG,EAAE;YACT,KAAK,EAAE,UAAU;YACjB,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,QAAQ,EACJ,+DAA+D;SACtE,CAAC;aACD,MAAM,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,QAAQ,EACJ,sDAAsD;SAC7D,CAAC;aACD,MAAM,CAAC,GAAG,EAAE;YACT,KAAK,EAAE,WAAW;YAClB,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,yCAAyC;SACtD,CAAC,CAAA;IACV,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAA6C;QACvD,IAAI,CAAC;YACD,MAAM,SAAS,GAAG,2BAAY,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;gBACvC,CAAC,CAAC,IAAI,CAAC,IAAI;gBACX,CAAC,CAAC,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5C,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YACzC,MAAM,QAAQ,GACV,cAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,QAAQ,CAAA;YAE9D,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ;gBAC7B,CAAC,CAAC,sBAAsB,CAAC,qBAAqB,CACxC,QAAQ,EACR,SAAS,EACT,IAAI,CAAC,GAAG,CACX;gBACH,CAAC,CAAC,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAA;YAE7D,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAC1C,WAAW,CACd,CAAA;YACD,OAAO,CAAC,GAAG,CACP,aAAa,eAAI,CAAC,IAAI,CAClB,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAC7C,mCAAmC,CACvC,CAAA;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,6BAAa,CAAC,SAAS,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAA;YAChE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,2BAA2B;IAC3B,4EAA4E;IAE5E;;OAEG;IACO,MAAM,CAAC,WAAW,CAAC,IAAY,EAAE,SAAiB;QACxD,OAAO;;eAEA,IAAA,uBAAS,EACZ,IAAI,EACJ,IAAI,CACP,GAAG,SAAS;;;;;;;;;CASpB,CAAA;IACG,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,qBAAqB,CAClC,IAAY,EACZ,SAAiB,EACjB,GAAY;QAEZ,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAA;QACxD,OAAO;;;;;;;;EAQb,YAAY,UAAU,IAAA,uBAAS,EAAC,IAAI,EAAE,IAAI,CAAC,GAAG,SAAS;;;;;;;;;CASxD,CAAA;IACG,CAAC;CACJ;AAtHD,wDAsHC", "file": "MigrationCreateCommand.js", "sourcesContent": ["import ansi from \"ansis\"\nimport path from \"path\"\nimport yargs from \"yargs\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { camelCase } from \"../util/StringUtils\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Creates a new migration file.\n */\nexport class MigrationCreateCommand implements yargs.CommandModule {\n    command = \"migration:create <path>\"\n    describe = \"Creates a new migration file.\"\n\n    builder(args: yargs.Argv) {\n        return args\n            .positional(\"path\", {\n                type: \"string\",\n                describe: \"Path of the migration file\",\n                demandOption: true,\n            })\n            .option(\"o\", {\n                alias: \"outputJs\",\n                type: \"boolean\",\n                default: false,\n                describe:\n                    \"Generate a migration file on Javascript instead of Typescript\",\n            })\n            .option(\"esm\", {\n                type: \"boolean\",\n                default: false,\n                describe:\n                    \"Generate a migration file on ESM instead of CommonJS\",\n            })\n            .option(\"t\", {\n                alias: \"timestamp\",\n                type: \"number\",\n                default: false,\n                describe: \"Custom timestamp for the migration name\",\n            })\n    }\n\n    async handler(args: yargs.Arguments<any & { path: string }>) {\n        try {\n            const timestamp = CommandUtils.getTimestamp(args.timestamp)\n            const inputPath = args.path.startsWith(\"/\")\n                ? args.path\n                : path.resolve(process.cwd(), args.path)\n            const filename = path.basename(inputPath)\n            const fullPath =\n                path.dirname(inputPath) + \"/\" + timestamp + \"-\" + filename\n\n            const fileContent = args.outputJs\n                ? MigrationCreateCommand.getJavascriptTemplate(\n                      filename,\n                      timestamp,\n                      args.esm,\n                  )\n                : MigrationCreateCommand.getTemplate(filename, timestamp)\n\n            await CommandUtils.createFile(\n                fullPath + (args.outputJs ? \".js\" : \".ts\"),\n                fileContent,\n            )\n            console.log(\n                `Migration ${ansi.blue(\n                    fullPath + (args.outputJs ? \".js\" : \".ts\"),\n                )} has been generated successfully.`,\n            )\n        } catch (err) {\n            PlatformTools.logCmdErr(\"Error during migration creation:\", err)\n            process.exit(1)\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets contents of the migration file.\n     */\n    protected static getTemplate(name: string, timestamp: number): string {\n        return `import { MigrationInterface, QueryRunner } from \"typeorm\";\n\nexport class ${camelCase(\n            name,\n            true,\n        )}${timestamp} implements MigrationInterface {\n\n    public async up(queryRunner: QueryRunner): Promise<void> {\n    }\n\n    public async down(queryRunner: QueryRunner): Promise<void> {\n    }\n\n}\n`\n    }\n\n    /**\n     * Gets contents of the migration file in Javascript.\n     */\n    protected static getJavascriptTemplate(\n        name: string,\n        timestamp: number,\n        esm: boolean,\n    ): string {\n        const exportMethod = esm ? \"export\" : \"module.exports =\"\n        return `/**\n * @typedef {import('typeorm').MigrationInterface} MigrationInterface\n */\n\n/**\n * @class\n * @implements {MigrationInterface}\n */\n${exportMethod} class ${camelCase(name, true)}${timestamp} {\n\n    async up(queryRunner) {\n    }\n\n    async down(queryRunner) {\n    }\n\n}\n`\n    }\n}\n"], "sourceRoot": ".."}