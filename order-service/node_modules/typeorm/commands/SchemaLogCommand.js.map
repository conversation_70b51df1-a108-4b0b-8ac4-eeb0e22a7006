{"version": 3, "sources": ["../../src/commands/SchemaLogCommand.ts"], "names": [], "mappings": ";;;;AAAA,0DAAwB;AACxB,wDAAuB;AACvB,8DAA6B;AAG7B,6DAAyD;AACzD,iDAA6C;AAE7C;;GAEG;AACH,MAAa,gBAAgB;IAA7B;QACI,YAAO,GAAG,YAAY,CAAA;QACtB,aAAQ,GACJ,sGAAsG;YACtG,+DAA+D,CAAA;IAkEvE,CAAC;IAhEG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC7B,KAAK,EAAE,GAAG;YACV,QAAQ,EACJ,6DAA6D;YACjE,YAAY,EAAE,IAAI;SACrB,CAAC,CAAA;IACN,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAAqB;QAC/B,IAAI,UAAU,GAA2B,SAAS,CAAA;QAClD,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,2BAAY,CAAC,cAAc,CAC1C,cAAI,CAAC,OAAO,CAAC,iBAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,UAAoB,CAAC,CACzD,CAAA;YACD,UAAU,CAAC,UAAU,CAAC;gBAClB,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,KAAK;gBACpB,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,KAAK;aACjB,CAAC,CAAA;YACF,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,MAAM;iBACtC,mBAAmB,EAAE;iBACrB,GAAG,EAAE,CAAA;YAEV,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,MAAM,CAAA,4FAA4F,CAC1G,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,aAAa,GAAG,EAAE,CAAC,QAAQ,CAC7B,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,EAChD,GAAG,CACN,CAAA;gBACD,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAA;gBACvC,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,MAAM;qBACN,IAAI,CAAA,iEAAiE,eAAI,CAAC,KAAK,CAChF,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,CAC1C,IAAI,CACR,CAAA;gBACD,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAA;gBAEvC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBACtC,IAAI,SAAS,GAAG,OAAO,CAAC,KAAK,CAAA;oBAC7B,SAAS,GAAG,SAAS,CAAC,IAAI,EAAE,CAAA;oBAC5B,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC;wBAC/B,CAAC,CAAC,SAAS;wBACX,CAAC,CAAC,SAAS,GAAG,GAAG,CAAA;oBACrB,OAAO,CAAC,GAAG,CAAC,6BAAa,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAA;gBACtD,CAAC,CAAC,CAAA;YACN,CAAC;YACD,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;QAC9B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,UAAU;gBACV,6BAAa,CAAC,SAAS,CACnB,sCAAsC,EACtC,GAAG,CACN,CAAA;YACL,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;CACJ;AAtED,4CAsEC", "file": "SchemaLogCommand.js", "sourcesContent": ["import ansi from \"ansis\"\nimport path from \"path\"\nimport process from \"process\"\nimport yargs from \"yargs\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Shows sql to be executed by schema:sync command.\n */\nexport class SchemaLogCommand implements yargs.CommandModule {\n    command = \"schema:log\"\n    describe =\n        \"Shows sql to be executed by schema:sync command. It shows sql log only for your default dataSource. \" +\n        \"To run update queries on a concrete connection use -c option.\"\n\n    builder(args: yargs.Argv) {\n        return args.option(\"dataSource\", {\n            alias: \"d\",\n            describe:\n                \"Path to the file where your DataSource instance is defined.\",\n            demandOption: true,\n        })\n    }\n\n    async handler(args: yargs.Arguments) {\n        let dataSource: DataSource | undefined = undefined\n        try {\n            dataSource = await CommandUtils.loadDataSource(\n                path.resolve(process.cwd(), args.dataSource as string),\n            )\n            dataSource.setOptions({\n                synchronize: false,\n                migrationsRun: false,\n                dropSchema: false,\n                logging: false,\n            })\n            await dataSource.initialize()\n\n            const sqlInMemory = await dataSource.driver\n                .createSchemaBuilder()\n                .log()\n\n            if (sqlInMemory.upQueries.length === 0) {\n                console.log(\n                    ansi.yellow`Your schema is up to date - there are no queries to be executed by schema synchronization.`,\n                )\n            } else {\n                const lineSeparator = \"\".padStart(\n                    63 + String(sqlInMemory.upQueries.length).length,\n                    \"-\",\n                )\n                console.log(ansi.yellow(lineSeparator))\n                console.log(\n                    ansi.yellow\n                        .bold`-- Schema synchronization will execute following sql queries (${ansi.white(\n                        sqlInMemory.upQueries.length.toString(),\n                    )}):`,\n                )\n                console.log(ansi.yellow(lineSeparator))\n\n                sqlInMemory.upQueries.forEach((upQuery) => {\n                    let sqlString = upQuery.query\n                    sqlString = sqlString.trim()\n                    sqlString = sqlString.endsWith(\";\")\n                        ? sqlString\n                        : sqlString + \";\"\n                    console.log(PlatformTools.highlightSql(sqlString))\n                })\n            }\n            await dataSource.destroy()\n        } catch (err) {\n            if (dataSource)\n                PlatformTools.logCmdErr(\n                    \"Error during schema synchronization:\",\n                    err,\n                )\n            process.exit(1)\n        }\n    }\n}\n"], "sourceRoot": ".."}