{"version": 3, "sources": ["../../src/commands/SchemaDropCommand.ts"], "names": [], "mappings": ";;;;AAAA,0DAAwB;AACxB,wDAAuB;AACvB,8DAA6B;AAG7B,6DAAyD;AACzD,iDAA6C;AAE7C;;GAEG;AACH,MAAa,iBAAiB;IAA9B;QACI,YAAO,GAAG,aAAa,CAAA;QACvB,aAAQ,GACJ,+DAA+D;YAC/D,kEAAkE,CAAA;IAuC1E,CAAC;IArCG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC7B,KAAK,EAAE,GAAG;YACV,QAAQ,EACJ,6DAA6D;YACjE,YAAY,EAAE,IAAI;SACrB,CAAC,CAAA;IACN,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAAqB;QAC/B,IAAI,UAAU,GAA2B,SAAS,CAAA;QAClD,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,2BAAY,CAAC,cAAc,CAC1C,cAAI,CAAC,OAAO,CAAC,iBAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,UAAoB,CAAC,CACzD,CAAA;YACD,UAAU,CAAC,UAAU,CAAC;gBAClB,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,KAAK;gBACpB,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;aAC/B,CAAC,CAAA;YACF,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAC7B,MAAM,UAAU,CAAC,YAAY,EAAE,CAAA;YAC/B,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;YAE1B,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,KAAK,CAAA,gDAAgD,CAC7D,CAAA;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,6BAAa,CAAC,SAAS,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAA;YAEzD,IAAI,UAAU,IAAI,UAAU,CAAC,aAAa;gBACtC,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;YAE9B,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;CACJ;AA3CD,8CA2CC", "file": "SchemaDropCommand.js", "sourcesContent": ["import ansi from \"ansis\"\nimport path from \"path\"\nimport process from \"process\"\nimport yargs from \"yargs\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Drops all tables of the database from the given dataSource.\n */\nexport class SchemaDropCommand implements yargs.CommandModule {\n    command = \"schema:drop\"\n    describe =\n        \"Drops all tables in the database on your default dataSource. \" +\n        \"To drop table of a concrete connection's database use -c option.\"\n\n    builder(args: yargs.Argv) {\n        return args.option(\"dataSource\", {\n            alias: \"d\",\n            describe:\n                \"Path to the file where your DataSource instance is defined.\",\n            demandOption: true,\n        })\n    }\n\n    async handler(args: yargs.Arguments) {\n        let dataSource: DataSource | undefined = undefined\n        try {\n            dataSource = await CommandUtils.loadDataSource(\n                path.resolve(process.cwd(), args.dataSource as string),\n            )\n            dataSource.setOptions({\n                synchronize: false,\n                migrationsRun: false,\n                dropSchema: false,\n                logging: [\"query\", \"schema\"],\n            })\n            await dataSource.initialize()\n            await dataSource.dropDatabase()\n            await dataSource.destroy()\n\n            console.log(\n                ansi.green`Database schema has been successfully dropped.`,\n            )\n        } catch (err) {\n            PlatformTools.logCmdErr(\"Error during schema drop:\", err)\n\n            if (dataSource && dataSource.isInitialized)\n                await dataSource.destroy()\n\n            process.exit(1)\n        }\n    }\n}\n"], "sourceRoot": ".."}