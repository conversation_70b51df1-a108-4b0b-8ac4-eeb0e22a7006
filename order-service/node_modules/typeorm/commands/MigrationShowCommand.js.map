{"version": 3, "sources": ["../../src/commands/MigrationShowCommand.ts"], "names": [], "mappings": ";;;;AACA,yDAAkC;AAElC,6DAAyD;AACzD,wDAAuB;AACvB,iDAA6C;AAE7C;;GAEG;AACH,MAAa,oBAAoB;IAAjC;QACI,YAAO,GAAG,gBAAgB,CAAA;QAC1B,aAAQ,GAAG,2DAA2D,CAAA;IAsC1E,CAAC;IApCG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC7B,KAAK,EAAE,GAAG;YACV,QAAQ,EACJ,6DAA6D;YACjE,YAAY,EAAE,IAAI;SACrB,CAAC,CAAA;IACN,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAAqB;QAC/B,IAAI,UAAU,GAA2B,SAAS,CAAA;QAClD,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,2BAAY,CAAC,cAAc,CAC1C,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,UAAoB,CAAC,CACzD,CAAA;YACD,UAAU,CAAC,UAAU,CAAC;gBAClB,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,KAAK;gBACpB,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,CAAC,QAAQ,CAAC;aACtB,CAAC,CAAA;YACF,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAC7B,MAAM,UAAU,CAAC,cAAc,EAAE,CAAA;YACjC,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;YAE1B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,6BAAa,CAAC,SAAS,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAA;YAE5D,IAAI,UAAU,IAAI,UAAU,CAAC,aAAa;gBACtC,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;YAE9B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;CACJ;AAxCD,oDAwCC", "file": "MigrationShowCommand.js", "sourcesContent": ["import { DataSource } from \"../data-source\"\nimport * as process from \"process\"\nimport * as yargs from \"yargs\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport path from \"path\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Shows all migrations and whether they have been run or not.\n */\nexport class MigrationShowCommand implements yargs.CommandModule {\n    command = \"migration:show\"\n    describe = \"Show all migrations and whether they have been run or not\"\n\n    builder(args: yargs.Argv) {\n        return args.option(\"dataSource\", {\n            alias: \"d\",\n            describe:\n                \"Path to the file where your DataSource instance is defined.\",\n            demandOption: true,\n        })\n    }\n\n    async handler(args: yargs.Arguments) {\n        let dataSource: DataSource | undefined = undefined\n        try {\n            dataSource = await CommandUtils.loadDataSource(\n                path.resolve(process.cwd(), args.dataSource as string),\n            )\n            dataSource.setOptions({\n                subscribers: [],\n                synchronize: false,\n                migrationsRun: false,\n                dropSchema: false,\n                logging: [\"schema\"],\n            })\n            await dataSource.initialize()\n            await dataSource.showMigrations()\n            await dataSource.destroy()\n\n            process.exit(0)\n        } catch (err) {\n            PlatformTools.logCmdErr(\"Error during migration show:\", err)\n\n            if (dataSource && dataSource.isInitialized)\n                await dataSource.destroy()\n\n            process.exit(1)\n        }\n    }\n}\n"], "sourceRoot": ".."}