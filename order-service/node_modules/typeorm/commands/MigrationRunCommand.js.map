{"version": 3, "sources": ["../../src/commands/MigrationRunCommand.ts"], "names": [], "mappings": ";;;;AAAA,wDAAuB;AACvB,yDAAkC;AAElC,6DAAyD;AAEzD,iDAA6C;AAE7C;;GAEG;AACH,MAAa,mBAAmB;IAAhC;QACI,YAAO,GAAG,eAAe,CAAA;QACzB,aAAQ,GAAG,8BAA8B,CAAA;IA6E7C,CAAC;IA3EG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI;aACN,MAAM,CAAC,YAAY,EAAE;YAClB,KAAK,EAAE,GAAG;YACV,QAAQ,EACJ,6DAA6D;YACjE,YAAY,EAAE,IAAI;SACrB,CAAC;aACD,MAAM,CAAC,aAAa,EAAE;YACnB,KAAK,EAAE,GAAG;YACV,OAAO,EAAE,SAAS;YAClB,QAAQ,EACJ,uFAAuF;SAC9F,CAAC;aACD,MAAM,CAAC,MAAM,EAAE;YACZ,KAAK,EAAE,GAAG;YACV,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,QAAQ,EACJ,+FAA+F;gBAC/F,gCAAgC;SACvC,CAAC,CAAA;IACV,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAAqB;QAC/B,IAAI,UAAU,GAA2B,SAAS,CAAA;QAClD,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,2BAAY,CAAC,cAAc,CAC1C,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,UAAoB,CAAC,CACzD,CAAA;YACD,UAAU,CAAC,UAAU,CAAC;gBAClB,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,KAAK;gBACpB,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC;aACxC,CAAC,CAAA;YACF,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,MAAM,OAAO,GAAG;gBACZ,WAAW,EACP,UAAU,CAAC,OAAO,CAAC,yBAAyB;oBAC3C,KAAiC;gBACtC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;aACjB,CAAA;YAED,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC;gBACb,KAAK,KAAK;oBACN,OAAO,CAAC,WAAW,GAAG,KAAK,CAAA;oBAC3B,MAAK;gBACT,KAAK,MAAM,CAAC;gBACZ,KAAK,OAAO;oBACR,OAAO,CAAC,WAAW,GAAG,MAAM,CAAA;oBAC5B,MAAK;gBACT,KAAK,MAAM;oBACP,OAAO,CAAC,WAAW,GAAG,MAAM,CAAA;oBAC5B,MAAK;gBACT,QAAQ;gBACR,OAAO;YACX,CAAC;YAED,MAAM,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;YACvC,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;YAE1B,4BAA4B;YAC5B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,6BAAa,CAAC,SAAS,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAA;YAE3D,IAAI,UAAU,IAAI,UAAU,CAAC,aAAa;gBACtC,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;YAE9B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;CACJ;AA/ED,kDA+EC", "file": "MigrationRunCommand.js", "sourcesContent": ["import path from \"path\"\nimport * as process from \"process\"\nimport * as yargs from \"yargs\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { DataSource } from \"../data-source\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Runs migration command.\n */\nexport class MigrationRunCommand implements yargs.CommandModule {\n    command = \"migration:run\"\n    describe = \"Runs all pending migrations.\"\n\n    builder(args: yargs.Argv) {\n        return args\n            .option(\"dataSource\", {\n                alias: \"d\",\n                describe:\n                    \"Path to the file where your DataSource instance is defined.\",\n                demandOption: true,\n            })\n            .option(\"transaction\", {\n                alias: \"t\",\n                default: \"default\",\n                describe:\n                    \"Indicates if transaction should be used or not for migration run. Enabled by default.\",\n            })\n            .option(\"fake\", {\n                alias: \"f\",\n                type: \"boolean\",\n                default: false,\n                describe:\n                    \"Fakes running the migrations if table schema has already been changed manually or externally \" +\n                    \"(e.g. through another project)\",\n            })\n    }\n\n    async handler(args: yargs.Arguments) {\n        let dataSource: DataSource | undefined = undefined\n        try {\n            dataSource = await CommandUtils.loadDataSource(\n                path.resolve(process.cwd(), args.dataSource as string),\n            )\n            dataSource.setOptions({\n                subscribers: [],\n                synchronize: false,\n                migrationsRun: false,\n                dropSchema: false,\n                logging: [\"query\", \"error\", \"schema\"],\n            })\n            await dataSource.initialize()\n\n            const options = {\n                transaction:\n                    dataSource.options.migrationsTransactionMode ??\n                    (\"all\" as \"all\" | \"none\" | \"each\"),\n                fake: !!args.f,\n            }\n\n            switch (args.t) {\n                case \"all\":\n                    options.transaction = \"all\"\n                    break\n                case \"none\":\n                case \"false\":\n                    options.transaction = \"none\"\n                    break\n                case \"each\":\n                    options.transaction = \"each\"\n                    break\n                default:\n                // noop\n            }\n\n            await dataSource.runMigrations(options)\n            await dataSource.destroy()\n\n            // exit process if no errors\n            process.exit(0)\n        } catch (err) {\n            PlatformTools.logCmdErr(\"Error during migration run:\", err)\n\n            if (dataSource && dataSource.isInitialized)\n                await dataSource.destroy()\n\n            process.exit(1)\n        }\n    }\n}\n"], "sourceRoot": ".."}