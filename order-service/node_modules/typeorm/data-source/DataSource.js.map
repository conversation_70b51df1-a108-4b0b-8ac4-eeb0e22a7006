{"version": 3, "sources": ["../../src/data-source/DataSource.ts"], "names": [], "mappings": ";;;AACA,oDAAwD;AAMxD,oFAAgF;AAChF,oCAMiB;AAMjB,sEAAkE;AAIlE,yFAAqF;AAErF,iFAA6E;AAC7E,2DAAuD;AACvD,uFAAmF;AAEnF,4EAAwE;AACxE,2DAAuD;AACvD,8EAA0E;AAG1E,oEAAgE;AAChE,qDAAiD;AAGjD,wEAAoE;AACpE,uDAAmD;AACnD,6DAAyD;AAEzD,qDAAiD;AAEjD,IAAA,qCAAqB,GAAE,CAAA;AAEvB;;;;;;;GAOG;AACH,MAAa,UAAU;IAkFnB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,OAA0B;QArF7B,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;QAgDjD;;WAEG;QACM,eAAU,GAAyB,EAAE,CAAA;QAE9C;;WAEG;QACM,gBAAW,GAAqC,EAAE,CAAA;QAE3D;;WAEG;QACM,oBAAe,GAAqB,EAAE,CAAA;QAE/C;;;WAGG;QACM,uBAAkB,GAAG,IAAI,GAAG,EAAqC,CAAA;QAmBtE,IAAA,qCAAqB,GAAE,CAAA;QACvB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,SAAS,CAAA;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,6BAAa,EAAE,CAAC,MAAM,CACpC,IAAI,CAAC,OAAO,CAAC,MAAM,EACnB,IAAI,CAAC,OAAO,CAAC,OAAO,CACvB,CAAA;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,6BAAa,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAA;QACzC,IAAI,CAAC,cAAc;YACf,OAAO,CAAC,cAAc,IAAI,IAAI,6CAAqB,EAAE,CAAA;QACzD,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,kBAAkB,CAAA;QACxE,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,KAAK;YACjC,CAAC,CAAC,IAAI,iDAAuB,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;YAC5C,CAAC,CAAC,SAAS,CAAA;QACf,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;IAC9B,CAAC;IAED,4EAA4E;IAC5E,mBAAmB;IACnB,4EAA4E;IAE5E;;;;OAIG;IACH,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,aAAa,CAAA;IAC7B,CAAC;IAED;;;;;OAKG;IACH,IAAI,YAAY;QACZ,IAAI,CAAC,iCAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC;YACnD,MAAM,IAAI,oBAAY,CAClB,6DAA6D,CAChE,CAAA;QAEL,OAAO,IAAI,CAAC,OAA6B,CAAA;IAC7C,CAAC;IAED;;;;OAIG;IACH,IAAI,YAAY;QACZ,IAAI,CAAC,iCAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC;YACnD,MAAM,IAAI,oBAAY,CAClB,2DAA2D,CAC9D,CAAA;QAEL,OAAO,IAAI,CAAC,OAAO,CAAA;IACvB,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAC5E;;OAEG;IACH,UAAU,CAAC,OAAmC;QAC1C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAEpC,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,GAAG,IAAI,6BAAa,EAAE,CAAC,MAAM,CACpC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EACrC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAC1C,CAAA;QACL,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAChD,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,gBAAgB,GAAG,IAAI,iDAAuB,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAA;QACtE,CAAC;QAED,8FAA8F;QAC9F,iGAAiG;QACjG,iEAAiE;QACjE,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,yBAAW,CAAC,kBAAkB,CACjD,IAAI,CAAC,OAAO,CACf,CAAC,QAAQ,CAAA;QACd,CAAC;QAED,oFAAoF;QAEpF,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,IAAI,CAAC,aAAa;YAClB,MAAM,IAAI,0CAAkC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAE3D,yCAAyC;QACzC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;QAE3B,6DAA6D;QAC7D,IAAI,IAAI,CAAC,gBAAgB;YAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAA;QAEhE,kDAAkD;QAClD,yBAAW,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAA;QAEjD,IAAI,CAAC;YACD,2DAA2D;YAC3D,MAAM,IAAI,CAAC,cAAc,EAAE,CAAA;YAE3B,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAA;YAEhC,yDAAyD;YACzD,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU;gBAAE,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;YAEtD,wDAAwD;YACxD,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa;gBAC1B,MAAM,IAAI,CAAC,aAAa,CAAC;oBACrB,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,yBAAyB;iBACtD,CAAC,CAAA;YAEN,wDAAwD;YACxD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW;gBAAE,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,qGAAqG;YACrG,gCAAgC;YAChC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;YACpB,MAAM,KAAK,CAAA;QACf,CAAC;QAED,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,EAAE,CAAA;IAC5B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,IAAI,CAAC,aAAa;YACnB,MAAM,IAAI,sCAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEvD,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAA;QAE9B,mEAAmE;QACnE,IAAI,IAAI,CAAC,gBAAgB;YAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAA;QAEnE,yBAAW,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAA;IACtD,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAAK;QACP,OAAO,IAAI,CAAC,OAAO,EAAE,CAAA;IACzB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,WAAW,CAAC,iBAA0B,KAAK;QAC7C,IAAI,CAAC,IAAI,CAAC,aAAa;YACnB,MAAM,IAAI,sCAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEvD,IAAI,cAAc;YAAE,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;QAE7C,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA;QACvD,MAAM,aAAa,CAAC,KAAK,EAAE,CAAA;IAC/B,CAAC;IAED;;;;OAIG;IACH,cAAc;IACd,KAAK,CAAC,YAAY;QACd,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,CAAC;YACD,IACI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;gBACpC,yBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;gBACtC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc;gBAC3C,yBAAW,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EACzC,CAAC;gBACC,MAAM,SAAS,GAAa,EAAE,CAAA;gBAC9B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;oBACtC,IACI,QAAQ,CAAC,QAAQ;wBACjB,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;wBAE3C,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;gBACzC,CAAC,CAAC,CAAA;gBACF,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACjD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;gBACxC,CAAC;gBAED,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACzB,MAAM,WAAW,CAAC,aAAa,EAAE,CAAA;gBACrC,CAAC;qBAAM,CAAC;oBACJ,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;wBAC/B,MAAM,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;oBAC7C,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,WAAW,CAAC,aAAa,EAAE,CAAA;YACrC,CAAC;QACL,CAAC;gBAAS,CAAC;YACP,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QAC/B,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa,CAAC,OAGnB;QACG,IAAI,CAAC,IAAI,CAAC,aAAa;YACnB,MAAM,IAAI,sCAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEvD,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,CAAC,IAAI,CAAC,CAAA;QACrD,iBAAiB,CAAC,WAAW;YACzB,OAAO,EAAE,WAAW;gBACpB,IAAI,CAAC,OAAO,EAAE,yBAAyB;gBACvC,KAAK,CAAA;QACT,iBAAiB,CAAC,IAAI,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAA;QAE3D,MAAM,iBAAiB,GACnB,MAAM,iBAAiB,CAAC,wBAAwB,EAAE,CAAA;QACtD,OAAO,iBAAiB,CAAA;IAC5B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAGvB;QACG,IAAI,CAAC,IAAI,CAAC,aAAa;YACnB,MAAM,IAAI,sCAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEvD,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,CAAC,IAAI,CAAC,CAAA;QACrD,iBAAiB,CAAC,WAAW;YACzB,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK,CAAA;QAC7C,iBAAiB,CAAC,IAAI,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAA;QAE3D,MAAM,iBAAiB,CAAC,iBAAiB,EAAE,CAAA;IAC/C,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,cAAc;QAChB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,MAAM,IAAI,sCAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACvD,CAAC;QACD,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,CAAC,IAAI,CAAC,CAAA;QACrD,OAAO,MAAM,iBAAiB,CAAC,cAAc,EAAE,CAAA;IACnD,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,MAAyB;QACjC,OAAO,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,MAAyB;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAC1C,IAAI,CAAC,QAAQ;YAAE,MAAM,IAAI,mCAA2B,CAAC,MAAM,CAAC,CAAA;QAE5D,OAAO,QAAQ,CAAA;IACnB,CAAC;IAED;;OAEG;IACH,aAAa,CACT,MAA4B;QAE5B,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;IAC7C,CAAC;IAED;;;OAGG;IACH,iBAAiB,CACb,MAA4B;QAE5B,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;IACjD,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,MAA4B;QAE5B,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC;YACzC,MAAM,IAAI,oBAAY,CAClB,8DAA8D,CACjE,CAAA;QAEL,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAQ,CAAA;IACpD,CAAC;IAED;;;;OAIG;IACH,mBAAmB,CAAI,gBAA+B;QAClD,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAA;IAC7D,CAAC;IAaD,KAAK,CAAC,WAAW,CACb,2BAEoD,EACpD,qBAAoE;QAEpE,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAC3B,2BAAkC,EAClC,qBAA4B,CAC/B,CAAA;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,WAAyB;QAEzB,IAAI,iCAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC;YAClD,MAAM,IAAI,oBAAY,CAAC,sCAAsC,CAAC,CAAA;QAElE,IAAI,WAAW,IAAI,WAAW,CAAC,UAAU;YACrC,MAAM,IAAI,+CAAuC,EAAE,CAAA;QAEvD,MAAM,eAAe,GAAG,WAAW,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAE/D,IAAI,CAAC;YACD,OAAO,MAAM,eAAe,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA,CAAC,oDAAoD;QAC9G,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,WAAW;gBAAE,MAAM,eAAe,CAAC,OAAO,EAAE,CAAA;QACrD,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,GAAG,CACL,OAA6B,EAC7B,GAAG,MAAiB;QAEpB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAA,yBAAW,EAAC;YACtC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,MAAM;SACtB,CAAC,CAAA;QAEF,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;IAC9C,CAAC;IAgBD;;OAEG;IACH,kBAAkB,CACd,cAAmD,EACnD,KAAc,EACd,WAAyB;QAEzB,IAAI,iCAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC;YAClD,MAAM,IAAI,oBAAY,CAAC,4CAA4C,CAAC,CAAA;QAExE,IAAI,KAAK,EAAE,CAAC;YACR,KAAK,GAAG,yBAAW,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;YAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAC7B,cAAsC,CACzC,CAAA;YACD,OAAO,IAAI,uCAAkB,CAAC,IAAI,EAAE,WAAW,CAAC;iBAC3C,MAAM,CAAC,KAAK,CAAC;iBACb,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;QACrC,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,uCAAkB,CACzB,IAAI,EACJ,cAAyC,CAC5C,CAAA;QACL,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACH,iBAAiB,CAAC,OAAwB,QAAQ;QAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;QACvD,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAA;QACrD,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAA;QAChD,OAAO,WAAW,CAAA;IACtB,CAAC;IAED;;OAEG;IACH,qBAAqB,CACjB,YAA+B,EAC/B,oBAA4B;QAE5B,MAAM,gBAAgB,GAClB,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,4BAA4B,CACvD,oBAAoB,CACvB,CAAA;QACL,IAAI,CAAC,gBAAgB;YACjB,MAAM,IAAI,oBAAY,CAClB,aAAa,oBAAoB,sBAAsB,YAAY,UAAU,CAChF,CAAA;QACL,IAAI,CAAC,gBAAgB,CAAC,YAAY;YAC9B,MAAM,IAAI,oBAAY,CAClB,aAAa,YAAY,IAAI,oBAAoB,8CAA8C;gBAC3F,yDAAyD,CAChE,CAAA;QAEL,OAAO,gBAAgB,CAAC,sBAAsB,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,WAAyB;QACzC,OAAO,IAAI,2CAAoB,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,CAAA;IAC/D,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,YAAY,CAClB,MAAyB;QAEzB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC3D,IAAI,eAAe;YAAE,OAAO,eAAe,CAAA;QAE3C,KAAK,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,IACI,iCAAe,CAAC,cAAc,CAAC,MAAM,CAAC;gBACtC,QAAQ,CAAC,IAAI,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI,EACvC,CAAC;gBACC,OAAO,QAAQ,CAAA;YACnB,CAAC;YACD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC7B,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC7B,IAAI,QAAQ,CAAC,SAAS,KAAK,MAAM,EAAE,CAAC;wBAChC,OAAO,QAAQ,CAAA;oBACnB,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,IACI,QAAQ,CAAC,IAAI,KAAK,MAAM;wBACxB,QAAQ,CAAC,SAAS,KAAK,MAAM,EAC/B,CAAC;wBACC,OAAO,QAAQ,CAAA;oBACnB,CAAC;gBACL,CAAC;YACL,CAAC;YACD,IACI,yBAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBACpC,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,EACjC,CAAC;gBACC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAClC,IAAI,QAAQ,CAAC,SAAS,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC;wBACrC,OAAO,QAAQ,CAAA;oBACnB,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,IACI,QAAQ,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;wBAC7B,QAAQ,CAAC,SAAS,KAAK,MAAM,CAAC,IAAI,EACpC,CAAC;wBACC,OAAO,QAAQ,CAAA;oBACnB,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAA;IACpB,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,cAAc;QAC1B,MAAM,yBAAyB,GAAG,IAAI,qDAAyB,CAAC,IAAI,CAAC,CAAA;QACrE,MAAM,uBAAuB,GAAG,IAAI,iDAAuB,EAAE,CAAA;QAE7D,wIAAwI;QACxI,MAAM,oBAAoB,GAAG,yBAAW,CAAC,gBAAgB,CACrD,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,EAAE,CACjC,CAAA;QACD,MAAM,WAAW,GAAG,MAAM,yBAAyB,CAAC,gBAAgB,CAChE,oBAAoB,CACvB,CAAA;QACD,yBAAW,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC,CAAA;QAEtD,yBAAyB;QACzB,MAAM,iBAAiB,GAAG,yBAAW,CAAC,gBAAgB,CAClD,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAC9B,CAAA;QACD,MAAM,eAAe,GACjB,MAAM,yBAAyB,CAAC,oBAAoB,CAChD,iBAAiB,CACpB,CAAA;QACL,yBAAW,CAAC,MAAM,CAAC,IAAI,EAAE;YACrB,eAAe,EAAE,eAAe;YAChC,kBAAkB,EAAE,IAAI,GAAG,CACvB,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CACjE;SACJ,CAAC,CAAA;QAEF,6BAA6B;QAC7B,MAAM,mBAAmB,GAAG,yBAAW,CAAC,gBAAgB,CACpD,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAChC,CAAA;QACD,MAAM,UAAU,GAAG,MAAM,yBAAyB,CAAC,eAAe,CAC9D,mBAAmB,CACtB,CAAA;QACD,yBAAW,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAA;QAEpD,iGAAiG;QACjG,uBAAuB,CAAC,YAAY,CAChC,IAAI,CAAC,eAAe,CAAC,MAAM,CACvB,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS,KAAK,MAAM,CAC9C,EACD,IAAI,CAAC,MAAM,CACd,CAAA;QAED,0CAA0C;QAC1C,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;YAC3C,IACI,iCAAe,CAAC,uBAAuB,CAAC,cAAc,CAAC,MAAM,CAAC,EAChE,CAAC;gBACC,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;YAC7C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,8BAA8B;QAC1B,IACI,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO;YACpC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EACjC,CAAC;YACC,MAAM,KAAK,GACP,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAGvB,CAAC,WAAW,CAAA;YACb,IAAI,KAAK,EAAE,CAAC;gBACR,OAAO,KAAK,CAAA;YAChB,CAAC;QACL,CAAC;QACD,OAAO,OAAO,CAAA;IAClB,CAAC;CACJ;AA7tBD,gCA6tBC", "file": "DataSource.js", "sourcesContent": ["import { Driver } from \"../driver/Driver\"\nimport { registerQueryBuilders } from \"../query-builder\"\nimport { Repository } from \"../repository/Repository\"\nimport { EntitySubscriberInterface } from \"../subscriber/EntitySubscriberInterface\"\nimport { EntityTarget } from \"../common/EntityTarget\"\nimport { ObjectType } from \"../common/ObjectType\"\nimport { EntityManager } from \"../entity-manager/EntityManager\"\nimport { DefaultNamingStrategy } from \"../naming-strategy/DefaultNamingStrategy\"\nimport {\n    CannotConnectAlreadyConnectedError,\n    CannotExecuteNotConnectedError,\n    EntityMetadataNotFoundError,\n    QueryRunnerProviderAlreadyReleasedError,\n    TypeORMError,\n} from \"../error\"\nimport { TreeRepository } from \"../repository/TreeRepository\"\nimport { NamingStrategyInterface } from \"../naming-strategy/NamingStrategyInterface\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { Logger } from \"../logger/Logger\"\nimport { MigrationInterface } from \"../migration/MigrationInterface\"\nimport { MigrationExecutor } from \"../migration/MigrationExecutor\"\nimport { Migration } from \"../migration/Migration\"\nimport { MongoRepository } from \"../repository/MongoRepository\"\nimport { MongoEntityManager } from \"../entity-manager/MongoEntityManager\"\nimport { EntityMetadataValidator } from \"../metadata-builder/EntityMetadataValidator\"\nimport { DataSourceOptions } from \"./DataSourceOptions\"\nimport { EntityManagerFactory } from \"../entity-manager/EntityManagerFactory\"\nimport { DriverFactory } from \"../driver/DriverFactory\"\nimport { ConnectionMetadataBuilder } from \"../connection/ConnectionMetadataBuilder\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\nimport { LoggerFactory } from \"../logger/LoggerFactory\"\nimport { QueryResultCacheFactory } from \"../cache/QueryResultCacheFactory\"\nimport { QueryResultCache } from \"../cache/QueryResultCache\"\nimport { SqljsEntityManager } from \"../entity-manager/SqljsEntityManager\"\nimport { RelationLoader } from \"../query-builder/RelationLoader\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { IsolationLevel } from \"../driver/types/IsolationLevel\"\nimport { ReplicationMode } from \"../driver/types/ReplicationMode\"\nimport { RelationIdLoader } from \"../query-builder/RelationIdLoader\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { buildSqlTag } from \"../util/SqlTagUtils\"\n\nregisterQueryBuilders()\n\n/**\n * DataSource is a pre-defined connection configuration to a specific database.\n * You can have multiple data sources connected (with multiple connections in it),\n * connected to multiple databases in your application.\n *\n * Before, it was called `Connection`, but now `Connection` is deprecated\n * because `Connection` isn't the best name for what it's actually is.\n */\nexport class DataSource {\n    readonly \"@instanceof\" = Symbol.for(\"DataSource\")\n\n    // -------------------------------------------------------------------------\n    // Public Readonly Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection name.\n     *\n     * @deprecated we don't need names anymore since we are going to drop all related methods relying on this property.\n     */\n    readonly name: string\n\n    /**\n     * Connection options.\n     */\n    readonly options: DataSourceOptions\n\n    /**\n     * Indicates if DataSource is initialized or not.\n     */\n    readonly isInitialized: boolean\n\n    /**\n     * Database driver used by this connection.\n     */\n    driver: Driver\n\n    /**\n     * EntityManager of this connection.\n     */\n    readonly manager: EntityManager\n\n    /**\n     * Naming strategy used in the connection.\n     */\n    namingStrategy: NamingStrategyInterface\n\n    /**\n     * Name for the metadata table\n     */\n    readonly metadataTableName: string\n\n    /**\n     * Logger used to log orm events.\n     */\n    logger: Logger\n\n    /**\n     * Migration instances that are registered for this connection.\n     */\n    readonly migrations: MigrationInterface[] = []\n\n    /**\n     * Entity subscriber instances that are registered for this connection.\n     */\n    readonly subscribers: EntitySubscriberInterface<any>[] = []\n\n    /**\n     * All entity metadatas that are registered for this connection.\n     */\n    readonly entityMetadatas: EntityMetadata[] = []\n\n    /**\n     * All entity metadatas that are registered for this connection.\n     * This is a copy of #.entityMetadatas property -> used for more performant searches.\n     */\n    readonly entityMetadatasMap = new Map<EntityTarget<any>, EntityMetadata>()\n\n    /**\n     * Used to work with query result cache.\n     */\n    queryResultCache?: QueryResultCache\n\n    /**\n     * Used to load relations and work with lazy relations.\n     */\n    readonly relationLoader: RelationLoader\n\n    readonly relationIdLoader: RelationIdLoader\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(options: DataSourceOptions) {\n        registerQueryBuilders()\n        this.name = options.name || \"default\"\n        this.options = options\n        this.logger = new LoggerFactory().create(\n            this.options.logger,\n            this.options.logging,\n        )\n        this.driver = new DriverFactory().create(this)\n        this.manager = this.createEntityManager()\n        this.namingStrategy =\n            options.namingStrategy || new DefaultNamingStrategy()\n        this.metadataTableName = options.metadataTableName || \"typeorm_metadata\"\n        this.queryResultCache = options.cache\n            ? new QueryResultCacheFactory(this).create()\n            : undefined\n        this.relationLoader = new RelationLoader(this)\n        this.relationIdLoader = new RelationIdLoader(this)\n        this.isInitialized = false\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Accessors\n    // -------------------------------------------------------------------------\n\n    /**\n     Indicates if DataSource is initialized or not.\n     *\n     * @deprecated use .isInitialized instead\n     */\n    get isConnected() {\n        return this.isInitialized\n    }\n\n    /**\n     * Gets the mongodb entity manager that allows to perform mongodb-specific repository operations\n     * with any entity in this connection.\n     *\n     * Available only in mongodb connections.\n     */\n    get mongoManager(): MongoEntityManager {\n        if (!InstanceChecker.isMongoEntityManager(this.manager))\n            throw new TypeORMError(\n                `MongoEntityManager is only available for MongoDB databases.`,\n            )\n\n        return this.manager as MongoEntityManager\n    }\n\n    /**\n     * Gets a sql.js specific Entity Manager that allows to perform special load and save operations\n     *\n     * Available only in connection with the sqljs driver.\n     */\n    get sqljsManager(): SqljsEntityManager {\n        if (!InstanceChecker.isSqljsEntityManager(this.manager))\n            throw new TypeORMError(\n                `SqljsEntityManager is only available for Sqljs databases.`,\n            )\n\n        return this.manager\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n    /**\n     * Updates current connection options with provided options.\n     */\n    setOptions(options: Partial<DataSourceOptions>): this {\n        Object.assign(this.options, options)\n\n        if (options.logger || options.logging) {\n            this.logger = new LoggerFactory().create(\n                options.logger || this.options.logger,\n                options.logging || this.options.logging,\n            )\n        }\n\n        if (options.namingStrategy) {\n            this.namingStrategy = options.namingStrategy\n        }\n\n        if (options.cache) {\n            this.queryResultCache = new QueryResultCacheFactory(this).create()\n        }\n\n        // todo: we must update the database in the driver as well, if it was set by setOptions method\n        //  in the future we need to refactor the code and remove \"database\" from the driver, and instead\n        //  use database (and options) from a single place - data source.\n        if (options.database) {\n            this.driver.database = DriverUtils.buildDriverOptions(\n                this.options,\n            ).database\n        }\n\n        // todo: need to take a look if we need to update schema and other \"poor\" properties\n\n        return this\n    }\n\n    /**\n     * Performs connection to the database.\n     * This method should be called once on application bootstrap.\n     * This method not necessarily creates database connection (depend on database type),\n     * but it also can setup a connection pool with database to use.\n     */\n    async initialize(): Promise<this> {\n        if (this.isInitialized)\n            throw new CannotConnectAlreadyConnectedError(this.name)\n\n        // connect to the database via its driver\n        await this.driver.connect()\n\n        // connect to the cache-specific database if cache is enabled\n        if (this.queryResultCache) await this.queryResultCache.connect()\n\n        // set connected status for the current connection\n        ObjectUtils.assign(this, { isInitialized: true })\n\n        try {\n            // build all metadatas registered in the current connection\n            await this.buildMetadatas()\n\n            await this.driver.afterConnect()\n\n            // if option is set - drop schema once connection is done\n            if (this.options.dropSchema) await this.dropDatabase()\n\n            // if option is set - automatically synchronize a schema\n            if (this.options.migrationsRun)\n                await this.runMigrations({\n                    transaction: this.options.migrationsTransactionMode,\n                })\n\n            // if option is set - automatically synchronize a schema\n            if (this.options.synchronize) await this.synchronize()\n        } catch (error) {\n            // if for some reason build metadata fail (for example validation error during entity metadata check)\n            // connection needs to be closed\n            await this.destroy()\n            throw error\n        }\n\n        return this\n    }\n\n    /**\n     * Performs connection to the database.\n     * This method should be called once on application bootstrap.\n     * This method not necessarily creates database connection (depend on database type),\n     * but it also can setup a connection pool with database to use.\n     *\n     * @deprecated use .initialize method instead\n     */\n    async connect(): Promise<this> {\n        return this.initialize()\n    }\n\n    /**\n     * Closes connection with the database.\n     * Once connection is closed, you cannot use repositories or perform any operations except opening connection again.\n     */\n    async destroy(): Promise<void> {\n        if (!this.isInitialized)\n            throw new CannotExecuteNotConnectedError(this.name)\n\n        await this.driver.disconnect()\n\n        // disconnect from the cache-specific database if cache was enabled\n        if (this.queryResultCache) await this.queryResultCache.disconnect()\n\n        ObjectUtils.assign(this, { isInitialized: false })\n    }\n\n    /**\n     * Closes connection with the database.\n     * Once connection is closed, you cannot use repositories or perform any operations except opening connection again.\n     *\n     * @deprecated use .destroy method instead\n     */\n    async close(): Promise<void> {\n        return this.destroy()\n    }\n\n    /**\n     * Creates database schema for all entities registered in this connection.\n     * Can be used only after connection to the database is established.\n     *\n     * @param dropBeforeSync If set to true then it drops the database with all its tables and data\n     */\n    async synchronize(dropBeforeSync: boolean = false): Promise<void> {\n        if (!this.isInitialized)\n            throw new CannotExecuteNotConnectedError(this.name)\n\n        if (dropBeforeSync) await this.dropDatabase()\n\n        const schemaBuilder = this.driver.createSchemaBuilder()\n        await schemaBuilder.build()\n    }\n\n    /**\n     * Drops the database and all its data.\n     * Be careful with this method on production since this method will erase all your database tables and their data.\n     * Can be used only after connection to the database is established.\n     */\n    // TODO rename\n    async dropDatabase(): Promise<void> {\n        const queryRunner = this.createQueryRunner()\n        try {\n            if (\n                this.driver.options.type === \"mssql\" ||\n                DriverUtils.isMySQLFamily(this.driver) ||\n                this.driver.options.type === \"aurora-mysql\" ||\n                DriverUtils.isSQLiteFamily(this.driver)\n            ) {\n                const databases: string[] = []\n                this.entityMetadatas.forEach((metadata) => {\n                    if (\n                        metadata.database &&\n                        databases.indexOf(metadata.database) === -1\n                    )\n                        databases.push(metadata.database)\n                })\n                if (databases.length === 0 && this.driver.database) {\n                    databases.push(this.driver.database)\n                }\n\n                if (databases.length === 0) {\n                    await queryRunner.clearDatabase()\n                } else {\n                    for (const database of databases) {\n                        await queryRunner.clearDatabase(database)\n                    }\n                }\n            } else {\n                await queryRunner.clearDatabase()\n            }\n        } finally {\n            await queryRunner.release()\n        }\n    }\n\n    /**\n     * Runs all pending migrations.\n     * Can be used only after connection to the database is established.\n     */\n    async runMigrations(options?: {\n        transaction?: \"all\" | \"none\" | \"each\"\n        fake?: boolean\n    }): Promise<Migration[]> {\n        if (!this.isInitialized)\n            throw new CannotExecuteNotConnectedError(this.name)\n\n        const migrationExecutor = new MigrationExecutor(this)\n        migrationExecutor.transaction =\n            options?.transaction ||\n            this.options?.migrationsTransactionMode ||\n            \"all\"\n        migrationExecutor.fake = (options && options.fake) || false\n\n        const successMigrations =\n            await migrationExecutor.executePendingMigrations()\n        return successMigrations\n    }\n\n    /**\n     * Reverts last executed migration.\n     * Can be used only after connection to the database is established.\n     */\n    async undoLastMigration(options?: {\n        transaction?: \"all\" | \"none\" | \"each\"\n        fake?: boolean\n    }): Promise<void> {\n        if (!this.isInitialized)\n            throw new CannotExecuteNotConnectedError(this.name)\n\n        const migrationExecutor = new MigrationExecutor(this)\n        migrationExecutor.transaction =\n            (options && options.transaction) || \"all\"\n        migrationExecutor.fake = (options && options.fake) || false\n\n        await migrationExecutor.undoLastMigration()\n    }\n\n    /**\n     * Lists all migrations and whether they have been run.\n     * Returns true if there are pending migrations\n     */\n    async showMigrations(): Promise<boolean> {\n        if (!this.isInitialized) {\n            throw new CannotExecuteNotConnectedError(this.name)\n        }\n        const migrationExecutor = new MigrationExecutor(this)\n        return await migrationExecutor.showMigrations()\n    }\n\n    /**\n     * Checks if entity metadata exist for the given entity class, target name or table name.\n     */\n    hasMetadata(target: EntityTarget<any>): boolean {\n        return !!this.findMetadata(target)\n    }\n\n    /**\n     * Gets entity metadata for the given entity class or schema name.\n     */\n    getMetadata(target: EntityTarget<any>): EntityMetadata {\n        const metadata = this.findMetadata(target)\n        if (!metadata) throw new EntityMetadataNotFoundError(target)\n\n        return metadata\n    }\n\n    /**\n     * Gets repository for the given entity.\n     */\n    getRepository<Entity extends ObjectLiteral>(\n        target: EntityTarget<Entity>,\n    ): Repository<Entity> {\n        return this.manager.getRepository(target)\n    }\n\n    /**\n     * Gets tree repository for the given entity class or name.\n     * Only tree-type entities can have a TreeRepository, like ones decorated with @Tree decorator.\n     */\n    getTreeRepository<Entity extends ObjectLiteral>(\n        target: EntityTarget<Entity>,\n    ): TreeRepository<Entity> {\n        return this.manager.getTreeRepository(target)\n    }\n\n    /**\n     * Gets mongodb-specific repository for the given entity class or name.\n     * Works only if connection is mongodb-specific.\n     */\n    getMongoRepository<Entity extends ObjectLiteral>(\n        target: EntityTarget<Entity>,\n    ): MongoRepository<Entity> {\n        if (!(this.driver.options.type === \"mongodb\"))\n            throw new TypeORMError(\n                `You can use getMongoRepository only for MongoDB connections.`,\n            )\n\n        return this.manager.getRepository(target) as any\n    }\n\n    /**\n     * Gets custom entity repository marked with @EntityRepository decorator.\n     *\n     * @deprecated use Repository.extend function to create a custom repository\n     */\n    getCustomRepository<T>(customRepository: ObjectType<T>): T {\n        return this.manager.getCustomRepository(customRepository)\n    }\n\n    /**\n     * Wraps given function execution (and all operations made there) into a transaction.\n     * All database operations must be executed using provided entity manager.\n     */\n    async transaction<T>(\n        runInTransaction: (entityManager: EntityManager) => Promise<T>,\n    ): Promise<T>\n    async transaction<T>(\n        isolationLevel: IsolationLevel,\n        runInTransaction: (entityManager: EntityManager) => Promise<T>,\n    ): Promise<T>\n    async transaction<T>(\n        isolationOrRunInTransaction:\n            | IsolationLevel\n            | ((entityManager: EntityManager) => Promise<T>),\n        runInTransactionParam?: (entityManager: EntityManager) => Promise<T>,\n    ): Promise<any> {\n        return this.manager.transaction(\n            isolationOrRunInTransaction as any,\n            runInTransactionParam as any,\n        )\n    }\n\n    /**\n     * Executes raw SQL query and returns raw database results.\n     *\n     * @see [Official docs](https://typeorm.io/data-source-api) for examples.\n     */\n    async query<T = any>(\n        query: string,\n        parameters?: any[],\n        queryRunner?: QueryRunner,\n    ): Promise<T> {\n        if (InstanceChecker.isMongoEntityManager(this.manager))\n            throw new TypeORMError(`Queries aren't supported by MongoDB.`)\n\n        if (queryRunner && queryRunner.isReleased)\n            throw new QueryRunnerProviderAlreadyReleasedError()\n\n        const usedQueryRunner = queryRunner || this.createQueryRunner()\n\n        try {\n            return await usedQueryRunner.query(query, parameters) // await is needed here because we are using finally\n        } finally {\n            if (!queryRunner) await usedQueryRunner.release()\n        }\n    }\n\n    /**\n     * Tagged template function that executes raw SQL query and returns raw database results.\n     * Template expressions are automatically transformed into database parameters.\n     * Raw query execution is supported only by relational databases (MongoDB is not supported).\n     * Note: Don't call this as a regular function, it is meant to be used with backticks to tag a template literal.\n     * Example: dataSource.sql`SELECT * FROM table_name WHERE id = ${id}`\n     */\n    async sql<T = any>(\n        strings: TemplateStringsArray,\n        ...values: unknown[]\n    ): Promise<T> {\n        const { query, parameters } = buildSqlTag({\n            driver: this.driver,\n            strings: strings,\n            expressions: values,\n        })\n\n        return await this.query(query, parameters)\n    }\n\n    /**\n     * Creates a new query builder that can be used to build a SQL query.\n     */\n    createQueryBuilder<Entity extends ObjectLiteral>(\n        entityClass: EntityTarget<Entity>,\n        alias: string,\n        queryRunner?: QueryRunner,\n    ): SelectQueryBuilder<Entity>\n\n    /**\n     * Creates a new query builder that can be used to build a SQL query.\n     */\n    createQueryBuilder(queryRunner?: QueryRunner): SelectQueryBuilder<any>\n\n    /**\n     * Creates a new query builder that can be used to build a SQL query.\n     */\n    createQueryBuilder<Entity extends ObjectLiteral>(\n        entityOrRunner?: EntityTarget<Entity> | QueryRunner,\n        alias?: string,\n        queryRunner?: QueryRunner,\n    ): SelectQueryBuilder<Entity> {\n        if (InstanceChecker.isMongoEntityManager(this.manager))\n            throw new TypeORMError(`Query Builder is not supported by MongoDB.`)\n\n        if (alias) {\n            alias = DriverUtils.buildAlias(this.driver, undefined, alias)\n            const metadata = this.getMetadata(\n                entityOrRunner as EntityTarget<Entity>,\n            )\n            return new SelectQueryBuilder(this, queryRunner)\n                .select(alias)\n                .from(metadata.target, alias)\n        } else {\n            return new SelectQueryBuilder(\n                this,\n                entityOrRunner as QueryRunner | undefined,\n            )\n        }\n    }\n\n    /**\n     * Creates a query runner used for perform queries on a single database connection.\n     * Using query runners you can control your queries to execute using single database connection and\n     * manually control your database transaction.\n     *\n     * Mode is used in replication mode and indicates whatever you want to connect\n     * to master database or any of slave databases.\n     * If you perform writes you must use master database,\n     * if you perform reads you can use slave databases.\n     */\n    createQueryRunner(mode: ReplicationMode = \"master\"): QueryRunner {\n        const queryRunner = this.driver.createQueryRunner(mode)\n        const manager = this.createEntityManager(queryRunner)\n        Object.assign(queryRunner, { manager: manager })\n        return queryRunner\n    }\n\n    /**\n     * Gets entity metadata of the junction table (many-to-many table).\n     */\n    getManyToManyMetadata(\n        entityTarget: EntityTarget<any>,\n        relationPropertyPath: string,\n    ) {\n        const relationMetadata =\n            this.getMetadata(entityTarget).findRelationWithPropertyPath(\n                relationPropertyPath,\n            )\n        if (!relationMetadata)\n            throw new TypeORMError(\n                `Relation \"${relationPropertyPath}\" was not found in ${entityTarget} entity.`,\n            )\n        if (!relationMetadata.isManyToMany)\n            throw new TypeORMError(\n                `Relation \"${entityTarget}#${relationPropertyPath}\" does not have a many-to-many relationship.` +\n                    `You can use this method only on many-to-many relations.`,\n            )\n\n        return relationMetadata.junctionEntityMetadata\n    }\n\n    /**\n     * Creates an Entity Manager for the current connection with the help of the EntityManagerFactory.\n     */\n    createEntityManager(queryRunner?: QueryRunner): EntityManager {\n        return new EntityManagerFactory().create(this, queryRunner)\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Finds exist entity metadata by the given entity class, target name or table name.\n     */\n    protected findMetadata(\n        target: EntityTarget<any>,\n    ): EntityMetadata | undefined {\n        const metadataFromMap = this.entityMetadatasMap.get(target)\n        if (metadataFromMap) return metadataFromMap\n\n        for (const [_, metadata] of this.entityMetadatasMap) {\n            if (\n                InstanceChecker.isEntitySchema(target) &&\n                metadata.name === target.options.name\n            ) {\n                return metadata\n            }\n            if (typeof target === \"string\") {\n                if (target.indexOf(\".\") !== -1) {\n                    if (metadata.tablePath === target) {\n                        return metadata\n                    }\n                } else {\n                    if (\n                        metadata.name === target ||\n                        metadata.tableName === target\n                    ) {\n                        return metadata\n                    }\n                }\n            }\n            if (\n                ObjectUtils.isObjectWithName(target) &&\n                typeof target.name === \"string\"\n            ) {\n                if (target.name.indexOf(\".\") !== -1) {\n                    if (metadata.tablePath === target.name) {\n                        return metadata\n                    }\n                } else {\n                    if (\n                        metadata.name === target.name ||\n                        metadata.tableName === target.name\n                    ) {\n                        return metadata\n                    }\n                }\n            }\n        }\n\n        return undefined\n    }\n\n    /**\n     * Builds metadatas for all registered classes inside this connection.\n     */\n    protected async buildMetadatas(): Promise<void> {\n        const connectionMetadataBuilder = new ConnectionMetadataBuilder(this)\n        const entityMetadataValidator = new EntityMetadataValidator()\n\n        // create subscribers instances if they are not disallowed from high-level (for example they can disallowed from migrations run process)\n        const flattenedSubscribers = ObjectUtils.mixedListToArray(\n            this.options.subscribers || [],\n        )\n        const subscribers = await connectionMetadataBuilder.buildSubscribers(\n            flattenedSubscribers,\n        )\n        ObjectUtils.assign(this, { subscribers: subscribers })\n\n        // build entity metadatas\n        const flattenedEntities = ObjectUtils.mixedListToArray(\n            this.options.entities || [],\n        )\n        const entityMetadatas =\n            await connectionMetadataBuilder.buildEntityMetadatas(\n                flattenedEntities,\n            )\n        ObjectUtils.assign(this, {\n            entityMetadatas: entityMetadatas,\n            entityMetadatasMap: new Map(\n                entityMetadatas.map((metadata) => [metadata.target, metadata]),\n            ),\n        })\n\n        // create migration instances\n        const flattenedMigrations = ObjectUtils.mixedListToArray(\n            this.options.migrations || [],\n        )\n        const migrations = await connectionMetadataBuilder.buildMigrations(\n            flattenedMigrations,\n        )\n        ObjectUtils.assign(this, { migrations: migrations })\n\n        // validate all created entity metadatas to make sure user created entities are valid and correct\n        entityMetadataValidator.validateMany(\n            this.entityMetadatas.filter(\n                (metadata) => metadata.tableType !== \"view\",\n            ),\n            this.driver,\n        )\n\n        // set current data source to the entities\n        for (const entityMetadata of entityMetadatas) {\n            if (\n                InstanceChecker.isBaseEntityConstructor(entityMetadata.target)\n            ) {\n                entityMetadata.target.useDataSource(this)\n            }\n        }\n    }\n\n    /**\n     * Get the replication mode SELECT queries should use for this datasource by default\n     */\n    defaultReplicationModeForReads(): ReplicationMode {\n        if (\n            \"replication\" in this.driver.options &&\n            this.driver.options.replication\n        ) {\n            const value = (\n                this.driver.options.replication as {\n                    defaultMode?: ReplicationMode\n                }\n            ).defaultMode\n            if (value) {\n                return value\n            }\n        }\n        return \"slave\"\n    }\n}\n"], "sourceRoot": ".."}