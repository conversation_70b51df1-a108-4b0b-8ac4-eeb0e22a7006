{"version": 3, "sources": ["../../src/subscriber/Broadcaster.ts"], "names": [], "mappings": ";;;AAKA,qDAAiD;AACjD,2DAAuD;AAiFvD;;GAEG;AACH,MAAa,WAAW;IACpB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAoB,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;IAAG,CAAC;IAEhD,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,KAAK,CAAC,SAAS,CACX,KAAQ,EACR,GAAG,IAAsC;QAEzC,MAAM,MAAM,GAAG,IAAI,qCAAiB,EAAE,CAAA;QAEtC,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,KAAK,OAAqB,CAAC,CAAA;QAEtE,IAAI,OAAO,iBAAiB,KAAK,UAAU,EAAE,CAAC;YAC1C,CAAC;YAAC,iBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAA;QAC3D,CAAC;QAED,MAAM,MAAM,CAAC,IAAI,EAAE,CAAA;IACvB,CAAC;IAED;;;;;;;OAOG;IACH,0BAA0B,CACtB,MAAyB,EACzB,QAAwB,EACxB,MAAiC;QAEjC,IAAI,MAAM,IAAI,QAAQ,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC;YAClD,QAAQ,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAChD,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;oBAChD,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IACI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC;oBACrD,UAAU,CAAC,YAAY,EACzB,CAAC;oBACC,MAAM,eAAe,GAAG,UAAU,CAAC,YAAY,CAAC;wBAC5C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,QAAQ;qBACrB,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,0BAA0B,CACtB,MAAyB,EACzB,QAAwB,EACxB,MAAsB,EACtB,cAA8B,EAC9B,cAAiC,EACjC,gBAAqC;QAErC,4BAA4B;QAC5B,IAAI,MAAM,IAAI,QAAQ,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC;YAClD,QAAQ,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAChD,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;oBAChD,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IACI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC;oBACrD,UAAU,CAAC,YAAY,EACzB,CAAC;oBACC,MAAM,eAAe,GAAG,UAAU,CAAC,YAAY,CAAC;wBAC5C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,QAAQ;wBAClB,cAAc,EAAE,cAAc;wBAC9B,cAAc,EAAE,cAAc,IAAI,EAAE;wBACpC,gBAAgB,EAAE,gBAAgB,IAAI,EAAE;qBAC3C,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,0BAA0B,CACtB,MAAyB,EACzB,QAAwB,EACxB,MAAsB,EACtB,cAA8B,EAC9B,UAA0B;QAE1B,IAAI,MAAM,IAAI,QAAQ,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC;YAClD,QAAQ,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAChD,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;oBAChD,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IACI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC;oBACrD,UAAU,CAAC,YAAY,EACzB,CAAC;oBACC,MAAM,eAAe,GAAG,UAAU,CAAC,YAAY,CAAC;wBAC5C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,QAAQ;wBAClB,cAAc,EAAE,cAAc;wBAC9B,QAAQ,EAAE,QAAQ,CAAC,mBAAmB,CAClC,cAAc,IAAI,UAAU,CAC/B;qBACJ,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,8BAA8B,CAC1B,MAAyB,EACzB,QAAwB,EACxB,MAAsB,EACtB,cAA8B,EAC9B,UAA0B;QAE1B,IAAI,MAAM,IAAI,QAAQ,CAAC,yBAAyB,CAAC,MAAM,EAAE,CAAC;YACtD,QAAQ,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACpD,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;oBAChD,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IACI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC;oBACrD,UAAU,CAAC,gBAAgB,EAC7B,CAAC;oBACC,MAAM,eAAe,GAAG,UAAU,CAAC,gBAAgB,CAAC;wBAChD,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,QAAQ;wBAClB,cAAc,EAAE,cAAc;wBAC9B,QAAQ,EAAE,QAAQ,CAAC,mBAAmB,CAClC,cAAc,IAAI,UAAU,CAC/B;qBACJ,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,2BAA2B,CACvB,MAAyB,EACzB,QAAwB,EACxB,MAAsB,EACtB,cAA8B,EAC9B,UAA0B;QAE1B,IAAI,MAAM,IAAI,QAAQ,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC;YACnD,QAAQ,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjD,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;oBAChD,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IACI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC;oBACrD,UAAU,CAAC,aAAa,EAC1B,CAAC;oBACC,MAAM,eAAe,GAAG,UAAU,CAAC,aAAa,CAAC;wBAC7C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,QAAQ;wBAClB,cAAc,EAAE,cAAc;wBAC9B,QAAQ,EAAE,QAAQ,CAAC,mBAAmB,CAClC,cAAc,IAAI,UAAU,CAC/B;qBACJ,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,yBAAyB,CACrB,MAAyB,EACzB,QAAwB,EACxB,MAAsB,EACtB,UAA0B;QAE1B,IAAI,MAAM,IAAI,QAAQ,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;YACjD,QAAQ,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC/C,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;oBAChD,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IACI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC;oBACrD,UAAU,CAAC,WAAW,EACxB,CAAC;oBACC,MAAM,eAAe,GAAG,UAAU,CAAC,WAAW,CAAC;wBAC3C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,QAAQ;wBAClB,QAAQ,EAAE,QAAQ,CAAC,mBAAmB,CAAC,UAAU,CAAC;qBACrD,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,yBAAyB,CACrB,MAAyB,EACzB,KAAa,EACb,UAA6B;QAE7B,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;oBACzB,MAAM,eAAe,GAAG,UAAU,CAAC,WAAW,CAAC;wBAC3C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,KAAK,EAAE,KAAK;wBACZ,UAAU,EAAE,UAAU;qBACzB,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,wBAAwB,CACpB,MAAyB,EACzB,KAAa,EACb,UAA6B,EAC7B,OAAgB,EAChB,aAAiC,EACjC,UAA2B,EAC3B,KAAsB;QAEtB,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;oBACxB,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC;wBAC1C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,KAAK,EAAE,KAAK;wBACZ,UAAU,EAAE,UAAU;wBACtB,OAAO,EAAE,OAAO;wBAChB,aAAa,EAAE,aAAa;wBAC5B,UAAU,EAAE,UAAU;wBACtB,KAAK,EAAE,KAAK;qBACf,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,oCAAoC,CAAC,MAAyB;QAC1D,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IAAI,UAAU,CAAC,sBAAsB,EAAE,CAAC;oBACpC,MAAM,eAAe,GAAG,UAAU,CAAC,sBAAsB,CAAC;wBACtD,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;qBACpC,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,mCAAmC,CAAC,MAAyB;QACzD,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IAAI,UAAU,CAAC,qBAAqB,EAAE,CAAC;oBACnC,MAAM,eAAe,GAAG,UAAU,CAAC,qBAAqB,CAAC;wBACrD,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;qBACpC,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,qCAAqC,CAAC,MAAyB;QAC3D,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IAAI,UAAU,CAAC,uBAAuB,EAAE,CAAC;oBACrC,MAAM,eAAe,GAAG,UAAU,CAAC,uBAAuB,CAAC;wBACvD,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;qBACpC,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,oCAAoC,CAAC,MAAyB;QAC1D,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IAAI,UAAU,CAAC,sBAAsB,EAAE,CAAC;oBACpC,MAAM,eAAe,GAAG,UAAU,CAAC,sBAAsB,CAAC;wBACtD,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;qBACpC,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,uCAAuC,CAAC,MAAyB;QAC7D,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IAAI,UAAU,CAAC,yBAAyB,EAAE,CAAC;oBACvC,MAAM,eAAe,GACjB,UAAU,CAAC,yBAAyB,CAAC;wBACjC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;qBACpC,CAAC,CAAA;oBACN,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,sCAAsC,CAAC,MAAyB;QAC5D,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IAAI,UAAU,CAAC,wBAAwB,EAAE,CAAC;oBACtC,MAAM,eAAe,GAAG,UAAU,CAAC,wBAAwB,CACvD;wBACI,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;qBACpC,CACJ,CAAA;oBACD,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,yBAAyB,CACrB,MAAyB,EACzB,QAAwB,EACxB,MAAsB,EACtB,cAA8B,EAC9B,cAAiC,EACjC,gBAAqC;QAErC,IAAI,MAAM,IAAI,QAAQ,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;YACjD,QAAQ,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC/C,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;oBAChD,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IACI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC;oBACrD,UAAU,CAAC,WAAW,EACxB,CAAC;oBACC,MAAM,eAAe,GAAG,UAAU,CAAC,WAAW,CAAC;wBAC3C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,QAAQ;wBAClB,cAAc,EAAE,cAAc;wBAC9B,cAAc,EAAE,cAAc,IAAI,EAAE;wBACpC,gBAAgB,EAAE,gBAAgB,IAAI,EAAE;qBAC3C,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,yBAAyB,CACrB,MAAyB,EACzB,QAAwB,EACxB,MAAsB,EACtB,cAA8B,EAC9B,UAA0B;QAE1B,IAAI,MAAM,IAAI,QAAQ,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;YACjD,QAAQ,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC/C,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;oBAChD,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IACI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC;oBACrD,UAAU,CAAC,WAAW,EACxB,CAAC;oBACC,MAAM,eAAe,GAAG,UAAU,CAAC,WAAW,CAAC;wBAC3C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,QAAQ;wBAClB,cAAc,EAAE,cAAc;wBAC9B,QAAQ,EAAE,QAAQ,CAAC,mBAAmB,CAClC,cAAc,IAAI,UAAU,CAC/B;qBACJ,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,6BAA6B,CACzB,MAAyB,EACzB,QAAwB,EACxB,MAAsB,EACtB,cAA8B,EAC9B,UAA0B;QAE1B,IAAI,MAAM,IAAI,QAAQ,CAAC,wBAAwB,CAAC,MAAM,EAAE,CAAC;YACrD,QAAQ,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACnD,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;oBAChD,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IACI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC;oBACrD,UAAU,CAAC,eAAe,EAC5B,CAAC;oBACC,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;wBAC/C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,QAAQ;wBAClB,cAAc,EAAE,cAAc;wBAC9B,QAAQ,EAAE,QAAQ,CAAC,mBAAmB,CAClC,cAAc,IAAI,UAAU,CAC/B;qBACJ,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,0BAA0B,CACtB,MAAyB,EACzB,QAAwB,EACxB,MAAsB,EACtB,cAA8B,EAC9B,UAA0B;QAE1B,IAAI,MAAM,IAAI,QAAQ,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC;YAClD,QAAQ,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAChD,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;oBAChD,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3D,IACI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC;oBACrD,UAAU,CAAC,YAAY,EACzB,CAAC;oBACC,MAAM,eAAe,GAAG,UAAU,CAAC,YAAY,CAAC;wBAC5C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;wBACjC,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,QAAQ;wBAClB,cAAc,EAAE,cAAc;wBAC9B,QAAQ,EAAE,QAAQ,CAAC,mBAAmB,CAClC,cAAc,IAAI,UAAU,CAC/B;qBACJ,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,yBAAyB,CACrB,MAAyB,EACzB,QAAwB,EACxB,QAAyB;QAEzB,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;IAC9D,CAAC;IAED;;;;;;;OAOG;IACH,kBAAkB,CACd,MAAyB,EACzB,QAAwB,EACxB,QAAyB;QAEzB,oEAAoE;QACpE,MAAM,kBAAkB,GACpB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAC1C,CAAC,UAAU,EAAE,EAAE,CACX,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC;YACrD,UAAU,CAAC,SAAS,CAC3B,CAAA;QAEL,IACI,QAAQ,CAAC,SAAS,CAAC,MAAM;YACzB,QAAQ,CAAC,kBAAkB,CAAC,MAAM;YAClC,kBAAkB,CAAC,MAAM,EAC3B,CAAC;YACC,6BAA6B;YAC7B,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM,CACtC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,YAAY,OAAO,CAAC,CAC3C,CAAA;YAED,sFAAsF;YACtF,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;gBAC5B,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;oBACpC,kBAAkB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;wBAClC,mHAAmH;wBACnH,IACI,QAAQ,CAAC,MAAM;4BACf,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC;4BAE7C,OAAM;wBAEV,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;wBAC7C,IAAI,yBAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;4BAC3B,IAAI,CAAC,kBAAkB,CACnB,MAAM,EACN,QAAQ,CAAC,qBAAqB,EAC9B,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CACzC,CAAA;oBACT,CAAC,CAAC,CAAA;gBACN,CAAC,CAAC,CAAA;YACN,CAAC;YAED,IAAI,QAAQ,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;gBACrC,QAAQ,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;oBAC7C,kBAAkB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;wBAClC,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;4BAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;4BAChD,IAAI,eAAe,YAAY,OAAO;gCAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;4BACzC,MAAM,CAAC,KAAK,EAAE,CAAA;wBAClB,CAAC;oBACL,CAAC,CAAC,CAAA;gBACN,CAAC,CAAC,CAAA;YACN,CAAC;YAED,kBAAkB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACtC,kBAAkB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBAClC,MAAM,eAAe,GAAG,UAAU,CAAC,SAAU,CAAC,MAAM,EAAE;wBAClD,MAAM;wBACN,QAAQ;wBACR,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU;wBACvC,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;qBACpC,CAAC,CAAA;oBACF,IAAI,eAAe,YAAY,OAAO;wBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;oBACzC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAClB,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;;OAGG;IACO,mBAAmB,CACzB,UAA0C,EAC1C,MAAyB;QAEzB,OAAO,CACH,CAAC,UAAU,CAAC,QAAQ;YACpB,CAAC,UAAU,CAAC,QAAQ,EAAE;YACtB,UAAU,CAAC,QAAQ,EAAE,KAAK,MAAM;YAChC,UAAU,CAAC,QAAQ,EAAE,KAAK,MAAM;YAChC,UAAU,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAC9C,CAAA;IACL,CAAC;CACJ;AAxzBD,kCAwzBC", "file": "Broadcaster.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { BroadcasterResult } from \"./BroadcasterResult\"\nimport { EntitySubscriberInterface } from \"./EntitySubscriberInterface\"\n\ninterface BroadcasterEvents {\n    BeforeQuery: (query: string, parameters: any[] | undefined) => void\n    AfterQuery: (\n        query: string,\n        parameters: any[] | undefined,\n        success: boolean,\n        executionTime: number | undefined,\n        rawResults: any | undefined,\n        error: any | undefined,\n    ) => void\n\n    BeforeTransactionCommit: () => void\n    AfterTransactionCommit: () => void\n    BeforeTransactionStart: () => void\n    AfterTransactionStart: () => void\n    BeforeTransactionRollback: () => void\n    AfterTransactionRollback: () => void\n\n    BeforeUpdate: (\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n        updatedColumns?: ColumnMetadata[],\n        updatedRelations?: RelationMetadata[],\n    ) => void\n    AfterUpdate: (\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n        updatedColumns?: ColumnMetadata[],\n        updatedRelations?: RelationMetadata[],\n    ) => void\n\n    BeforeInsert: (\n        metadata: EntityMetadata,\n        entity: ObjectLiteral | undefined,\n    ) => void\n    AfterInsert: (\n        metadata: EntityMetadata,\n        entity: ObjectLiteral | undefined,\n    ) => void\n\n    BeforeRemove: (\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n    ) => void\n    AfterRemove: (\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n    ) => void\n\n    BeforeSoftRemove: (\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n    ) => void\n    AfterSoftRemove: (\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n    ) => void\n\n    BeforeRecover: (\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n    ) => void\n    AfterRecover: (\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n    ) => void\n\n    Load: (metadata: EntityMetadata, entities: ObjectLiteral[]) => void\n}\n\n/**\n * Broadcaster provides a helper methods to broadcast events to the subscribers.\n */\nexport class Broadcaster {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(private queryRunner: QueryRunner) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    async broadcast<U extends keyof BroadcasterEvents>(\n        event: U,\n        ...args: Parameters<BroadcasterEvents[U]>\n    ): Promise<void> {\n        const result = new BroadcasterResult()\n\n        const broadcastFunction = this[`broadcast${event}Event` as keyof this]\n\n        if (typeof broadcastFunction === \"function\") {\n            ;(broadcastFunction as any).call(this, result, ...args)\n        }\n\n        await result.wait()\n    }\n\n    /**\n     * Broadcasts \"BEFORE_INSERT\" event.\n     * Before insert event is executed before entity is being inserted to the database for the first time.\n     * All subscribers and entity listeners who listened to this event will be executed at this point.\n     * Subscribers and entity listeners can return promises, it will wait until they are resolved.\n     *\n     * Note: this method has a performance-optimized code organization, do not change code structure.\n     */\n    broadcastBeforeInsertEvent(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entity: undefined | ObjectLiteral,\n    ): void {\n        if (entity && metadata.beforeInsertListeners.length) {\n            metadata.beforeInsertListeners.forEach((listener) => {\n                if (listener.isAllowed(entity)) {\n                    const executionResult = listener.execute(entity)\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (\n                    this.isAllowedSubscriber(subscriber, metadata.target) &&\n                    subscriber.beforeInsert\n                ) {\n                    const executionResult = subscriber.beforeInsert({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        entity: entity,\n                        metadata: metadata,\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"BEFORE_UPDATE\" event.\n     * Before update event is executed before entity is being updated in the database.\n     * All subscribers and entity listeners who listened to this event will be executed at this point.\n     * Subscribers and entity listeners can return promises, it will wait until they are resolved.\n     *\n     * Note: this method has a performance-optimized code organization, do not change code structure.\n     */\n    broadcastBeforeUpdateEvent(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n        updatedColumns?: ColumnMetadata[],\n        updatedRelations?: RelationMetadata[],\n    ): void {\n        // todo: send relations too?\n        if (entity && metadata.beforeUpdateListeners.length) {\n            metadata.beforeUpdateListeners.forEach((listener) => {\n                if (listener.isAllowed(entity)) {\n                    const executionResult = listener.execute(entity)\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (\n                    this.isAllowedSubscriber(subscriber, metadata.target) &&\n                    subscriber.beforeUpdate\n                ) {\n                    const executionResult = subscriber.beforeUpdate({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        entity: entity,\n                        metadata: metadata,\n                        databaseEntity: databaseEntity,\n                        updatedColumns: updatedColumns || [],\n                        updatedRelations: updatedRelations || [],\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"BEFORE_REMOVE\" event.\n     * Before remove event is executed before entity is being removed from the database.\n     * All subscribers and entity listeners who listened to this event will be executed at this point.\n     * Subscribers and entity listeners can return promises, it will wait until they are resolved.\n     *\n     * Note: this method has a performance-optimized code organization, do not change code structure.\n     */\n    broadcastBeforeRemoveEvent(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n        identifier?: ObjectLiteral,\n    ): void {\n        if (entity && metadata.beforeRemoveListeners.length) {\n            metadata.beforeRemoveListeners.forEach((listener) => {\n                if (listener.isAllowed(entity)) {\n                    const executionResult = listener.execute(entity)\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (\n                    this.isAllowedSubscriber(subscriber, metadata.target) &&\n                    subscriber.beforeRemove\n                ) {\n                    const executionResult = subscriber.beforeRemove({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        entity: entity,\n                        metadata: metadata,\n                        databaseEntity: databaseEntity,\n                        entityId: metadata.getEntityIdMixedMap(\n                            databaseEntity ?? identifier,\n                        ),\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"BEFORE_SOFT_REMOVE\" event.\n     * Before soft remove event is executed before entity is being soft removed from the database.\n     * All subscribers and entity listeners who listened to this event will be executed at this point.\n     * Subscribers and entity listeners can return promises, it will wait until they are resolved.\n     *\n     * Note: this method has a performance-optimized code organization, do not change code structure.\n     */\n    broadcastBeforeSoftRemoveEvent(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n        identifier?: ObjectLiteral,\n    ): void {\n        if (entity && metadata.beforeSoftRemoveListeners.length) {\n            metadata.beforeSoftRemoveListeners.forEach((listener) => {\n                if (listener.isAllowed(entity)) {\n                    const executionResult = listener.execute(entity)\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (\n                    this.isAllowedSubscriber(subscriber, metadata.target) &&\n                    subscriber.beforeSoftRemove\n                ) {\n                    const executionResult = subscriber.beforeSoftRemove({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        entity: entity,\n                        metadata: metadata,\n                        databaseEntity: databaseEntity,\n                        entityId: metadata.getEntityIdMixedMap(\n                            databaseEntity ?? identifier,\n                        ),\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"BEFORE_RECOVER\" event.\n     * Before recover event is executed before entity is being recovered in the database.\n     * All subscribers and entity listeners who listened to this event will be executed at this point.\n     * Subscribers and entity listeners can return promises, it will wait until they are resolved.\n     *\n     * Note: this method has a performance-optimized code organization, do not change code structure.\n     */\n    broadcastBeforeRecoverEvent(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n        identifier?: ObjectLiteral,\n    ): void {\n        if (entity && metadata.beforeRecoverListeners.length) {\n            metadata.beforeRecoverListeners.forEach((listener) => {\n                if (listener.isAllowed(entity)) {\n                    const executionResult = listener.execute(entity)\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (\n                    this.isAllowedSubscriber(subscriber, metadata.target) &&\n                    subscriber.beforeRecover\n                ) {\n                    const executionResult = subscriber.beforeRecover({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        entity: entity,\n                        metadata: metadata,\n                        databaseEntity: databaseEntity,\n                        entityId: metadata.getEntityIdMixedMap(\n                            databaseEntity ?? identifier,\n                        ),\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"AFTER_INSERT\" event.\n     * After insert event is executed after entity is being persisted to the database for the first time.\n     * All subscribers and entity listeners who listened to this event will be executed at this point.\n     * Subscribers and entity listeners can return promises, it will wait until they are resolved.\n     *\n     * Note: this method has a performance-optimized code organization, do not change code structure.\n     */\n    broadcastAfterInsertEvent(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        identifier?: ObjectLiteral,\n    ): void {\n        if (entity && metadata.afterInsertListeners.length) {\n            metadata.afterInsertListeners.forEach((listener) => {\n                if (listener.isAllowed(entity)) {\n                    const executionResult = listener.execute(entity)\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (\n                    this.isAllowedSubscriber(subscriber, metadata.target) &&\n                    subscriber.afterInsert\n                ) {\n                    const executionResult = subscriber.afterInsert({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        entity: entity,\n                        metadata: metadata,\n                        entityId: metadata.getEntityIdMixedMap(identifier),\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"BEFORE_QUERY\" event.\n     */\n    broadcastBeforeQueryEvent(\n        result: BroadcasterResult,\n        query: string,\n        parameters: undefined | any[],\n    ): void {\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (subscriber.beforeQuery) {\n                    const executionResult = subscriber.beforeQuery({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        query: query,\n                        parameters: parameters,\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"AFTER_QUERY\" event.\n     */\n    broadcastAfterQueryEvent(\n        result: BroadcasterResult,\n        query: string,\n        parameters: undefined | any[],\n        success: boolean,\n        executionTime: undefined | number,\n        rawResults: undefined | any,\n        error: undefined | any,\n    ): void {\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (subscriber.afterQuery) {\n                    const executionResult = subscriber.afterQuery({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        query: query,\n                        parameters: parameters,\n                        success: success,\n                        executionTime: executionTime,\n                        rawResults: rawResults,\n                        error: error,\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"BEFORE_TRANSACTION_START\" event.\n     */\n    broadcastBeforeTransactionStartEvent(result: BroadcasterResult): void {\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (subscriber.beforeTransactionStart) {\n                    const executionResult = subscriber.beforeTransactionStart({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"AFTER_TRANSACTION_START\" event.\n     */\n    broadcastAfterTransactionStartEvent(result: BroadcasterResult): void {\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (subscriber.afterTransactionStart) {\n                    const executionResult = subscriber.afterTransactionStart({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"BEFORE_TRANSACTION_COMMIT\" event.\n     */\n    broadcastBeforeTransactionCommitEvent(result: BroadcasterResult): void {\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (subscriber.beforeTransactionCommit) {\n                    const executionResult = subscriber.beforeTransactionCommit({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"AFTER_TRANSACTION_COMMIT\" event.\n     */\n    broadcastAfterTransactionCommitEvent(result: BroadcasterResult): void {\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (subscriber.afterTransactionCommit) {\n                    const executionResult = subscriber.afterTransactionCommit({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"BEFORE_TRANSACTION_ROLLBACK\" event.\n     */\n    broadcastBeforeTransactionRollbackEvent(result: BroadcasterResult): void {\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (subscriber.beforeTransactionRollback) {\n                    const executionResult =\n                        subscriber.beforeTransactionRollback({\n                            connection: this.queryRunner.connection,\n                            queryRunner: this.queryRunner,\n                            manager: this.queryRunner.manager,\n                        })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"AFTER_TRANSACTION_ROLLBACK\" event.\n     */\n    broadcastAfterTransactionRollbackEvent(result: BroadcasterResult): void {\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (subscriber.afterTransactionRollback) {\n                    const executionResult = subscriber.afterTransactionRollback(\n                        {\n                            connection: this.queryRunner.connection,\n                            queryRunner: this.queryRunner,\n                            manager: this.queryRunner.manager,\n                        },\n                    )\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"AFTER_UPDATE\" event.\n     * After update event is executed after entity is being updated in the database.\n     * All subscribers and entity listeners who listened to this event will be executed at this point.\n     * Subscribers and entity listeners can return promises, it will wait until they are resolved.\n     *\n     * Note: this method has a performance-optimized code organization, do not change code structure.\n     */\n    broadcastAfterUpdateEvent(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n        updatedColumns?: ColumnMetadata[],\n        updatedRelations?: RelationMetadata[],\n    ): void {\n        if (entity && metadata.afterUpdateListeners.length) {\n            metadata.afterUpdateListeners.forEach((listener) => {\n                if (listener.isAllowed(entity)) {\n                    const executionResult = listener.execute(entity)\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (\n                    this.isAllowedSubscriber(subscriber, metadata.target) &&\n                    subscriber.afterUpdate\n                ) {\n                    const executionResult = subscriber.afterUpdate({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        entity: entity,\n                        metadata: metadata,\n                        databaseEntity: databaseEntity,\n                        updatedColumns: updatedColumns || [],\n                        updatedRelations: updatedRelations || [],\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"AFTER_REMOVE\" event.\n     * After remove event is executed after entity is being removed from the database.\n     * All subscribers and entity listeners who listened to this event will be executed at this point.\n     * Subscribers and entity listeners can return promises, it will wait until they are resolved.\n     *\n     * Note: this method has a performance-optimized code organization, do not change code structure.\n     */\n    broadcastAfterRemoveEvent(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n        identifier?: ObjectLiteral,\n    ): void {\n        if (entity && metadata.afterRemoveListeners.length) {\n            metadata.afterRemoveListeners.forEach((listener) => {\n                if (listener.isAllowed(entity)) {\n                    const executionResult = listener.execute(entity)\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (\n                    this.isAllowedSubscriber(subscriber, metadata.target) &&\n                    subscriber.afterRemove\n                ) {\n                    const executionResult = subscriber.afterRemove({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        entity: entity,\n                        metadata: metadata,\n                        databaseEntity: databaseEntity,\n                        entityId: metadata.getEntityIdMixedMap(\n                            databaseEntity ?? identifier,\n                        ),\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"AFTER_SOFT_REMOVE\" event.\n     * After soft remove event is executed after entity is being soft removed from the database.\n     * All subscribers and entity listeners who listened to this event will be executed at this point.\n     * Subscribers and entity listeners can return promises, it will wait until they are resolved.\n     *\n     * Note: this method has a performance-optimized code organization, do not change code structure.\n     */\n    broadcastAfterSoftRemoveEvent(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n        identifier?: ObjectLiteral,\n    ): void {\n        if (entity && metadata.afterSoftRemoveListeners.length) {\n            metadata.afterSoftRemoveListeners.forEach((listener) => {\n                if (listener.isAllowed(entity)) {\n                    const executionResult = listener.execute(entity)\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (\n                    this.isAllowedSubscriber(subscriber, metadata.target) &&\n                    subscriber.afterSoftRemove\n                ) {\n                    const executionResult = subscriber.afterSoftRemove({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        entity: entity,\n                        metadata: metadata,\n                        databaseEntity: databaseEntity,\n                        entityId: metadata.getEntityIdMixedMap(\n                            databaseEntity ?? identifier,\n                        ),\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * Broadcasts \"AFTER_RECOVER\" event.\n     * After recover event is executed after entity is being recovered in the database.\n     * All subscribers and entity listeners who listened to this event will be executed at this point.\n     * Subscribers and entity listeners can return promises, it will wait until they are resolved.\n     *\n     * Note: this method has a performance-optimized code organization, do not change code structure.\n     */\n    broadcastAfterRecoverEvent(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entity?: ObjectLiteral,\n        databaseEntity?: ObjectLiteral,\n        identifier?: ObjectLiteral,\n    ): void {\n        if (entity && metadata.afterRecoverListeners.length) {\n            metadata.afterRecoverListeners.forEach((listener) => {\n                if (listener.isAllowed(entity)) {\n                    const executionResult = listener.execute(entity)\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n\n        if (this.queryRunner.connection.subscribers.length) {\n            this.queryRunner.connection.subscribers.forEach((subscriber) => {\n                if (\n                    this.isAllowedSubscriber(subscriber, metadata.target) &&\n                    subscriber.afterRecover\n                ) {\n                    const executionResult = subscriber.afterRecover({\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                        entity: entity,\n                        metadata: metadata,\n                        databaseEntity: databaseEntity,\n                        entityId: metadata.getEntityIdMixedMap(\n                            databaseEntity ?? identifier,\n                        ),\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                }\n            })\n        }\n    }\n\n    /**\n     * @deprecated Use `broadcastLoadForAllEvent`\n     */\n    broadcastLoadEventsForAll(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entities: ObjectLiteral[],\n    ): void {\n        return this.broadcastLoadEvent(result, metadata, entities)\n    }\n\n    /**\n     * Broadcasts \"AFTER_LOAD\" event for all given entities, and their sub-entities.\n     * After load event is executed after entity has been loaded from the database.\n     * All subscribers and entity listeners who listened to this event will be executed at this point.\n     * Subscribers and entity listeners can return promises, it will wait until they are resolved.\n     *\n     * Note: this method has a performance-optimized code organization, do not change code structure.\n     */\n    broadcastLoadEvent(\n        result: BroadcasterResult,\n        metadata: EntityMetadata,\n        entities: ObjectLiteral[],\n    ): void {\n        // Calculate which subscribers are fitting for the given entity type\n        const fittingSubscribers =\n            this.queryRunner.connection.subscribers.filter(\n                (subscriber) =>\n                    this.isAllowedSubscriber(subscriber, metadata.target) &&\n                    subscriber.afterLoad,\n            )\n\n        if (\n            metadata.relations.length ||\n            metadata.afterLoadListeners.length ||\n            fittingSubscribers.length\n        ) {\n            // todo: check why need this?\n            const nonPromiseEntities = entities.filter(\n                (entity) => !(entity instanceof Promise),\n            )\n\n            // collect load events for all children entities that were loaded with the main entity\n            if (metadata.relations.length) {\n                metadata.relations.forEach((relation) => {\n                    nonPromiseEntities.forEach((entity) => {\n                        // in lazy relations we cannot simply access to entity property because it will cause a getter and a database query\n                        if (\n                            relation.isLazy &&\n                            !entity.hasOwnProperty(relation.propertyName)\n                        )\n                            return\n\n                        const value = relation.getEntityValue(entity)\n                        if (ObjectUtils.isObject(value))\n                            this.broadcastLoadEvent(\n                                result,\n                                relation.inverseEntityMetadata,\n                                Array.isArray(value) ? value : [value],\n                            )\n                    })\n                })\n            }\n\n            if (metadata.afterLoadListeners.length) {\n                metadata.afterLoadListeners.forEach((listener) => {\n                    nonPromiseEntities.forEach((entity) => {\n                        if (listener.isAllowed(entity)) {\n                            const executionResult = listener.execute(entity)\n                            if (executionResult instanceof Promise)\n                                result.promises.push(executionResult)\n                            result.count++\n                        }\n                    })\n                })\n            }\n\n            fittingSubscribers.forEach((subscriber) => {\n                nonPromiseEntities.forEach((entity) => {\n                    const executionResult = subscriber.afterLoad!(entity, {\n                        entity,\n                        metadata,\n                        connection: this.queryRunner.connection,\n                        queryRunner: this.queryRunner,\n                        manager: this.queryRunner.manager,\n                    })\n                    if (executionResult instanceof Promise)\n                        result.promises.push(executionResult)\n                    result.count++\n                })\n            })\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Checks if subscriber's methods can be executed by checking if its don't listen to the particular entity,\n     * or listens our entity.\n     */\n    protected isAllowedSubscriber(\n        subscriber: EntitySubscriberInterface<any>,\n        target: Function | string,\n    ): boolean {\n        return (\n            !subscriber.listenTo ||\n            !subscriber.listenTo() ||\n            subscriber.listenTo() === Object ||\n            subscriber.listenTo() === target ||\n            subscriber.listenTo().isPrototypeOf(target)\n        )\n    }\n}\n"], "sourceRoot": ".."}