{"name": "typeorm", "version": "0.3.25", "description": "Data-Mapper ORM for TypeScript and ES2021+. Supports MySQL/MariaDB, PostgreSQL, MS SQL Server, Oracle, SAP HANA, SQLite, MongoDB databases.", "homepage": "https://typeorm.io", "bugs": {"url": "https://github.com/typeorm/typeorm/issues"}, "repository": {"type": "git", "url": "https://github.com/typeorm/typeorm.git"}, "funding": "https://opencollective.com/typeorm", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "exports": {".": {"types": "./index.d.ts", "node": {"import": "./index.mjs", "require": "./index.js", "types": "./index.d.ts"}, "browser": {"require": "./index.js", "import": "./browser/index.js", "default": "./index.js"}}, "./browser": {"types": "./index.d.ts", "default": "./browser/index.js"}, "./*.js": "./*.js", "./*": {"require": "./*.js", "import": "./*"}}, "main": "./index.js", "module": "./index.mjs", "browser": {"./browser/connection/ConnectionOptionsReader.js": "./browser/platform/BrowserConnectionOptionsReaderDummy.js", "./browser/connection/options-reader/ConnectionOptionsXmlReader.js": "./browser/platform/BrowserConnectionOptionsReaderDummy.js", "./browser/connection/options-reader/ConnectionOptionsYmlReader.js": "./browser/platform/BrowserConnectionOptionsReaderDummy.js", "./browser/driver/aurora-data-api/AuroraDataApiDriver.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/better-sqlite3/BetterSqlite3Driver.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/cockroachdb/CockroachDriver.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/mongodb/MongoDriver.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/mongodb/MongoQueryRunner.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/mongodb/bson.typings.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/mongodb/typings.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/mysql/MysqlDriver.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/oracle/OracleDriver.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/postgres/PostgresDriver.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/sap/SapDriver.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/sqlite/SqliteDriver.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/driver/sqlserver/SqlServerDriver.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/entity-manager/MongoEntityManager.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/logger/FileLogger.js": "./browser/platform/BrowserFileLoggerDummy.js", "./browser/platform/PlatformTools.js": "./browser/platform/BrowserPlatformTools.js", "./browser/repository/MongoRepository.js": "./browser/platform/BrowserDisabledDriversDummy.js", "./browser/util/DirectoryExportedClassesLoader.js": "./browser/platform/BrowserDirectoryExportedClassesLoader.js", "./index.js": "./browser/index.js", "./index.mjs": "./browser/index.js"}, "types": "./index.d.ts", "bin": {"typeorm": "./cli.js", "typeorm-ts-node-commonjs": "./cli-ts-node-commonjs.js", "typeorm-ts-node-esm": "./cli-ts-node-esm.js"}, "scripts": {"changelog": "standard-changelog", "compile": "rimraf ./build && tsc", "docs:dev": "cd docs && npm run start", "format": "prettier --cache --write \"./**/*.ts\"", "format:ci": "prettier --check \"./**/*.ts\"", "lint": "eslint .", "pack": "gulp pack", "package": "gulp package", "pre-commit": "lint-staged", "prepare": "husky", "publish:preview": "pkg-pr-new publish './build/package' --pnpm --template='./sample/playground'", "test": "npm run compile && npm run test:fast --", "test:ci": "mocha --bail", "test:fast": "mocha", "typecheck": "tsc --noEmit", "watch": "tsc --watch"}, "dependencies": {"@sqltools/formatter": "^1.2.5", "ansis": "^3.17.0", "app-root-path": "^3.1.0", "buffer": "^6.0.3", "dayjs": "^1.11.13", "debug": "^4.4.0", "dedent": "^1.6.0", "dotenv": "^16.4.7", "glob": "^10.4.5", "sha.js": "^2.4.11", "sql-highlight": "^6.0.0", "tslib": "^2.8.1", "uuid": "^11.1.0", "yargs": "^17.7.2"}, "devDependencies": {"@eslint/js": "^9.28.0", "@sap/hana-client": "^2.24.21", "@tsconfig/node16": "^16.1.4", "@types/chai": "^4.3.20", "@types/chai-as-promised": "^7.1.8", "@types/debug": "^4.1.12", "@types/gulp-rename": "^2.0.6", "@types/gulp-sourcemaps": "^0.0.38", "@types/mocha": "^10.0.10", "@types/node": "^16.18.126", "@types/sha.js": "^2.4.4", "@types/sinon": "^10.0.20", "@types/sinon-chai": "^4.0.0", "@types/source-map-support": "^0.5.10", "@types/yargs": "^17.0.33", "better-sqlite3": "^8.7.0", "chai": "^4.5.0", "chai-as-promised": "^7.1.2", "class-transformer": "^0.5.1", "eslint": "^9.28.0", "globals": "^16.2.0", "gulp": "^4.0.2", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.4", "gulp-shell": "^0.8.0", "gulp-sourcemaps": "^3.0.0", "gulp-typescript": "^6.0.0-alpha.1", "gulpclass": "^0.2.0", "hdb-pool": "^0.1.6", "husky": "^9.1.7", "lint-staged": "^15.5.2", "mocha": "^10.8.2", "mongodb": "^6.15.0", "mssql": "^11.0.1", "mysql": "^2.18.1", "mysql2": "^3.14.0", "nyc": "^17.1.0", "oracledb": "^6.8.0", "pg": "^8.14.1", "pg-query-stream": "^4.8.1", "pkg-pr-new": "^0.0.43", "prettier": "^2.8.8", "redis": "^4.7.0", "remap-istanbul": "^0.13.0", "rimraf": "^5.0.10", "sinon": "^15.2.0", "sinon-chai": "^3.7.0", "sort-package-json": "^2.15.1", "source-map-support": "^0.5.21", "sql.js": "^1.13.0", "sqlite3": "^5.1.7", "standard-changelog": "^6.0.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1"}, "peerDependencies": {"@google-cloud/spanner": "^5.18.0 || ^6.0.0 || ^7.0.0", "@sap/hana-client": "^2.12.25", "better-sqlite3": "^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0", "hdb-pool": "^0.1.6", "ioredis": "^5.0.4", "mongodb": "^5.8.0 || ^6.0.0", "mssql": "^9.1.1 || ^10.0.1 || ^11.0.1", "mysql2": "^2.2.5 || ^3.0.1", "oracledb": "^6.3.0", "pg": "^8.5.1", "pg-native": "^3.0.0", "pg-query-stream": "^4.0.0", "redis": "^3.1.1 || ^4.0.0", "reflect-metadata": "^0.1.14 || ^0.2.0", "sql.js": "^1.4.0", "sqlite3": "^5.0.3", "ts-node": "^10.7.0", "typeorm-aurora-data-api-driver": "^2.0.0 || ^3.0.0"}, "peerDependenciesMeta": {"@google-cloud/spanner": {"optional": true}, "@sap/hana-client": {"optional": true}, "better-sqlite3": {"optional": true}, "hdb-pool": {"optional": true}, "ioredis": {"optional": true}, "mongodb": {"optional": true}, "mssql": {"optional": true}, "mysql2": {"optional": true}, "oracledb": {"optional": true}, "pg": {"optional": true}, "pg-native": {"optional": true}, "pg-query-stream": {"optional": true}, "redis": {"optional": true}, "sql.js": {"optional": true}, "sqlite3": {"optional": true}, "ts-node": {"optional": true}, "typeorm-aurora-data-api-driver": {"optional": true}}, "engines": {"node": ">=16.13.0"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/typeorm", "logo": "https://opencollective.com/opencollective/logo.txt"}, "readmeFilename": "README.md", "tags": ["orm", "typescript", "typescript-orm", "ma<PERSON>b", "mariadb-orm", "mysql", "mysql-orm", "oracle", "oracle-orm", "postgresql", "postgresql-orm", "sap-hana", "sap-hana-orm", "spanner", "cloud-spanner", "cloud-spanner-orm", "sqlite", "sqlite-orm", "sql-server", "sql-server-orm"]}