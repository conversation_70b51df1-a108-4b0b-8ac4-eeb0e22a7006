{"version": 3, "sources": ["../browser/src/container.ts"], "names": [], "mappings": "AA6BA;;;;;GAKG;AACH,MAAM,gBAAgB,GAAuB,IAAI,CAAC;IAAA;QAGtC,cAAS,GAAsC,EAAE,CAAA;IAc7D,CAAC;IAZG,GAAG,CAAI,SAA2B;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAA;QAC/D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,QAAQ,GAAG;gBACP,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,IAAK,SAAyB,EAAE;aAC3C,CAAA;YACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACjC,CAAC;QAED,OAAO,QAAQ,CAAC,MAAM,CAAA;IAC1B,CAAC;CACJ,CAAC,EAAE,CAAA;AAEJ,IAAI,aAAiC,CAAA;AACrC,IAAI,oBAAqD,CAAA;AAEzD;;;;GAIG;AACH,MAAM,UAAU,YAAY,CACxB,YAAgC,EAChC,OAA6B;IAE7B,aAAa,GAAG,YAAY,CAAA;IAC5B,oBAAoB,GAAG,OAAO,CAAA;AAClC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,gBAAgB,CAAI,SAA2B;IAC3D,IAAI,aAAa,EAAE,CAAC;QAChB,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;YAC7C,IAAI,QAAQ;gBAAE,OAAO,QAAQ,CAAA;YAE7B,IAAI,CAAC,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,QAAQ;gBACvD,OAAO,QAAQ,CAAA;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,gBAAgB;gBAC/D,MAAM,KAAK,CAAA;QACnB,CAAC;IACL,CAAC;IACD,OAAO,gBAAgB,CAAC,GAAG,CAAI,SAAS,CAAC,CAAA;AAC7C,CAAC", "file": "container.js", "sourcesContent": ["/**\n * Container options.\n *\n * @deprecated\n */\nexport interface UseContainerOptions {\n    /**\n     * If set to true, then default container will be used in the case if given container haven't returned anything.\n     */\n    fallback?: boolean\n\n    /**\n     * If set to true, then default container will be used in the case if given container thrown an exception.\n     */\n    fallbackOnErrors?: boolean\n}\n\n/**\n * @deprecated\n */\nexport type ContainedType<T> = { new (...args: any[]): T } | Function\n\n/**\n * @deprecated\n */\nexport interface ContainerInterface {\n    get<T>(someClass: ContainedType<T>): T\n}\n\n/**\n * Container to be used by this library for inversion control. If container was not implicitly set then by default\n * container simply creates a new instance of the given class.\n *\n * @deprecated\n */\nconst defaultContainer: ContainerInterface = new (class\n    implements ContainerInterface\n{\n    private instances: { type: Function; object: any }[] = []\n\n    get<T>(someClass: ContainedType<T>): T {\n        let instance = this.instances.find((i) => i.type === someClass)\n        if (!instance) {\n            instance = {\n                type: someClass,\n                object: new (someClass as new () => T)(),\n            }\n            this.instances.push(instance)\n        }\n\n        return instance.object\n    }\n})()\n\nlet userContainer: ContainerInterface\nlet userContainerOptions: UseContainerOptions | undefined\n\n/**\n * Sets container to be used by this library.\n *\n * @deprecated\n */\nexport function useContainer(\n    iocContainer: ContainerInterface,\n    options?: UseContainerOptions,\n) {\n    userContainer = iocContainer\n    userContainerOptions = options\n}\n\n/**\n * Gets the IOC container used by this library.\n *\n * @deprecated\n */\nexport function getFromContainer<T>(someClass: ContainedType<T>): T {\n    if (userContainer) {\n        try {\n            const instance = userContainer.get(someClass)\n            if (instance) return instance\n\n            if (!userContainerOptions || !userContainerOptions.fallback)\n                return instance\n        } catch (error) {\n            if (!userContainerOptions || !userContainerOptions.fallbackOnErrors)\n                throw error\n        }\n    }\n    return defaultContainer.get<T>(someClass)\n}\n"], "sourceRoot": "."}