{"version": 3, "sources": ["../browser/src/logger/DebugLogger.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAA;AACjD,OAAO,EAAE,KAAK,EAAY,MAAM,OAAO,CAAA;AAIvC;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,cAAc;IAA/C;;QACI;;WAEG;QACK,WAAM,GAA6B;YACvC,GAAG,EAAE,KAAK,CAAC,aAAa,CAAC;YACzB,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC;YAC3B,IAAI,EAAE,KAAK,CAAC,cAAc,CAAC;YAC3B,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC;YAC7B,KAAK,EAAE,KAAK,CAAC,mBAAmB,CAAC;YACjC,aAAa,EAAE,KAAK,CAAC,qBAAqB,CAAC;YAC3C,YAAY,EAAE,KAAK,CAAC,oBAAoB,CAAC;YACzC,cAAc,EAAE,KAAK,CAAC,gBAAgB,CAAC;YACvC,SAAS,EAAE,KAAK,CAAC,mBAAmB,CAAC;SACxC,CAAA;IAuEL,CAAC;IArEG;;OAEG;IACO,eAAe,CAAC,IAAgC;QACtD,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,OAAO;gBACR,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAA;YAEvC,KAAK,aAAa;gBACd,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAA;YAE7C,KAAK,YAAY;gBACb,OAAO,IAAI,CAAA;YAEf,KAAK,QAAQ,CAAC;YACd,KAAK,cAAc;gBACf,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,OAAO,CAAA;YAE9C,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAA;YAE3C,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAA;YAErC,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAA;YAEtC,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAA;YAEtC;gBACI,OAAO,KAAK,CAAA;QACpB,CAAC;IACL,CAAC;IAED;;OAEG;IACO,QAAQ,CACd,KAAe,EACf,UAAqC,EACrC,WAAyB;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE;YACjD,wBAAwB,EAAE,KAAK;SAClC,CAAC,CAAA;QAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC7B,MAAM,kBAAkB,GAAG,OAAO,CAAC,IAAI,IAAI,KAAK,CAAA;YAEhD,IAAI,kBAAkB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACpC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACjB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAC3B,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,OAAO,CAClB,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;gBACpD,CAAC;gBAED,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;oBAClD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAC3B,aAAa,EACb,OAAO,CAAC,UAAU,CACrB,CAAA;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;CACJ", "file": "DebugLogger.js", "sourcesContent": ["import { AbstractLogger } from \"./AbstractLogger\"\nimport { debug, Debugger } from \"debug\"\nimport { LogLevel, LogMessage, LogMessageType } from \"./Logger\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\n\n/**\n * Performs logging of the events in TypeORM via debug library.\n */\nexport class DebugLogger extends AbstractLogger {\n    /**\n     * Object with all debug logger.\n     */\n    private logger: Record<string, Debugger> = {\n        log: debug(\"typeorm:log\"),\n        info: debug(\"typeorm:info\"),\n        warn: debug(\"typeorm:warn\"),\n        error: debug(\"typeorm:error\"),\n        query: debug(\"typeorm:query:log\"),\n        \"query-error\": debug(\"typeorm:query:error\"),\n        \"query-slow\": debug(\"typeorm:query:slow\"),\n        \"schema-build\": debug(\"typeorm:schema\"),\n        migration: debug(\"typeorm:migration\"),\n    }\n\n    /**\n     * Check is logging for level or message type is enabled.\n     */\n    protected isLogEnabledFor(type?: LogLevel | LogMessageType) {\n        switch (type) {\n            case \"query\":\n                return this.logger[\"query\"].enabled\n\n            case \"query-error\":\n                return this.logger[\"query-error\"].enabled\n\n            case \"query-slow\":\n                return true\n\n            case \"schema\":\n            case \"schema-build\":\n                return this.logger[\"schema-build\"].enabled\n\n            case \"migration\":\n                return this.logger[\"migration\"].enabled\n\n            case \"log\":\n                return this.logger[\"log\"].enabled\n\n            case \"info\":\n                return this.logger[\"info\"].enabled\n\n            case \"warn\":\n                return this.logger[\"warn\"].enabled\n\n            default:\n                return false\n        }\n    }\n\n    /**\n     * Write log to specific output.\n     */\n    protected writeLog(\n        level: LogLevel,\n        logMessage: LogMessage | LogMessage[],\n        queryRunner?: QueryRunner,\n    ) {\n        const messages = this.prepareLogMessages(logMessage, {\n            appendParameterAsComment: false,\n        })\n\n        for (const message of messages) {\n            const messageTypeOrLevel = message.type ?? level\n\n            if (messageTypeOrLevel in this.logger) {\n                if (message.prefix) {\n                    this.logger[messageTypeOrLevel](\n                        message.prefix,\n                        message.message,\n                    )\n                } else {\n                    this.logger[messageTypeOrLevel](message.message)\n                }\n\n                if (message.parameters && message.parameters.length) {\n                    this.logger[messageTypeOrLevel](\n                        \"parameters:\",\n                        message.parameters,\n                    )\n                }\n            }\n        }\n    }\n}\n"], "sourceRoot": ".."}