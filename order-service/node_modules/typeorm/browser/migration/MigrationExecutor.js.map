{"version": 3, "sources": ["../browser/src/migration/MigrationExecutor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,+BAA+B,CAAA;AAErD,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAA;AAGvC,OAAO,EAAE,cAAc,EAAE,MAAM,oCAAoC,CAAA;AAEnE,OAAO,EAAE,qCAAqC,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AAC9E,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AAEzD;;GAEG;AACH,MAAM,OAAO,iBAAiB;IA+B1B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACc,UAAsB,EACtB,WAAyB;QADzB,eAAU,GAAV,UAAU,CAAY;QACtB,gBAAW,GAAX,WAAW,CAAc;QApCvC,4EAA4E;QAC5E,oBAAoB;QACpB,4EAA4E;QAE5E;;;;;WAKG;QACH,gBAAW,GAA4B,KAAK,CAAA;QA4BxC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAc,CAAA;QACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAA;QAChD,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAA;QAClC,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAA;QAC9B,IAAI,CAAC,mBAAmB;YACpB,UAAU,CAAC,OAAO,CAAC,mBAAmB,IAAI,YAAY,CAAA;QAC1D,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACxD,IAAI,CAAC,mBAAmB,EACxB,MAAM,EACN,QAAQ,CACX,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACI,KAAK,CAAC,gBAAgB,CAAC,SAAoB;QAC9C,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;YAC9C,MAAM,IAAI,CAAC,+BAA+B,CAAC,WAAW,CAAC,CAAA;YAEvD,wDAAwD;YACxD,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA;YAClE,IAAI,eAAe,CAAC,oBAAoB,CAAC,aAAa,CAAC,EAAE,CAAC;gBACtD,MAAM,aAAa,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAA;YACnE,CAAC;YAED,MAAM,WAAW,CAAC,eAAe,EAAE,CAAA;YACnC,MAAO,SAAS,CAAC,QAAgB,CAAC,EAAE,CAAC,WAAW,CAAC,CAAA;YACjD,MAAM,WAAW,CAAC,cAAc,EAAE,CAAA;YAClC,MAAM,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAA;YAE1D,OAAO,SAAS,CAAA;QACpB,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB;QACzB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,qBAAqB;QAC9B,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;YAC9C,MAAM,IAAI,CAAC,+BAA+B,CAAC,WAAW,CAAC,CAAA;YAEvD,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAA;QACzD,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB;QAC7B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACnD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAE7D,OAAO,aAAa,CAAC,MAAM,CACvB,CAAC,SAAS,EAAE,EAAE,CACV,CAAC,kBAAkB,CAAC,IAAI,CACpB,CAAC,iBAAiB,EAAE,EAAE,CAClB,iBAAiB,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAChD,CACR,CAAA;IACL,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,SAAoB;QACvC,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAC9B,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,SAAS,CAAC,CAC7C,CAAA;IACL,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,SAAoB;QACvC,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAC9B,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,SAAS,CAAC,CAC7C,CAAA;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,cAAc;QAChB,IAAI,sBAAsB,GAAG,KAAK,CAAA;QAClC,MAAM,WAAW,GACb,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;QAC3D,iDAAiD;QACjD,MAAM,IAAI,CAAC,+BAA+B,CAAC,WAAW,CAAC,CAAA;QAEvD,iEAAiE;QACjE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACxD,WAAW,CACd,CAAA;QAED,+CAA+C;QAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAE1C,KAAK,MAAM,SAAS,IAAI,aAAa,EAAE,CAAC;YACpC,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,IAAI,CAC7C,CAAC,iBAAiB,EAAE,EAAE,CAClB,iBAAiB,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAChD,CAAA;YAED,IAAI,iBAAiB,EAAE,CAAC;gBACpB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,OAAO,iBAAiB,CAAC,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE,CAClD,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,sBAAsB,GAAG,IAAI,CAAA;gBAC7B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC,CAAA;YAClE,CAAC;QACL,CAAC;QAED,oDAAoD;QACpD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QAC/B,CAAC;QAED,OAAO,sBAAsB,CAAA;IACjC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,wBAAwB;QAC1B,MAAM,WAAW,GACb,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;QAC3D,kDAAkD;QAClD,MAAM,IAAI,CAAC,+BAA+B,CAAC,WAAW,CAAC,CAAA;QAEvD,4DAA4D;QAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA;QAClE,IAAI,eAAe,CAAC,oBAAoB,CAAC,aAAa,CAAC,EAAE,CAAC;YACtD,MAAM,aAAa,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAA;QACnE,CAAC;QAED,iEAAiE;QACjE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACxD,WAAW,CACd,CAAA;QAED,gDAAgD;QAChD,MAAM,yBAAyB,GAC3B,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,CAAA;QAExD,+CAA+C;QAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAE1C,uDAAuD;QACvD,MAAM,iBAAiB,GAAgB,EAAE,CAAA;QAEzC,gDAAgD;QAChD,MAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE;YACzD,8CAA8C;YAC9C,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,IAAI,CAC7C,CAAC,iBAAiB,EAAE,EAAE,CAClB,iBAAiB,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAChD,CAAA;YACD,IAAI,iBAAiB;gBAAE,OAAO,KAAK,CAAA;YAEnC,2EAA2E;YAC3E,8FAA8F;YAC9F,sNAAsN;YAEtN,mFAAmF;YACnF,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAA;QAEF,uDAAuD;QACvD,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;YAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,2BAA2B,CAAC,CAAA;YAClE,oDAAoD;YACpD,IAAI,CAAC,IAAI,CAAC,WAAW;gBAAE,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;YAClD,OAAO,EAAE,CAAA;QACb,CAAC;QAED,4CAA4C;QAC5C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,GAAG,kBAAkB,CAAC,MAAM,iDAAiD,CAChF,CAAA;QACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,GAAG,aAAa,CAAC,MAAM,4CAA4C,CACtE,CAAA;QACD,IAAI,yBAAyB;YACzB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,GACI,yBAAyB,CAAC,IAC9B,uDAAuD,IAAI,IAAI,CAC3D,yBAAyB,CAAC,SAAS,CACtC,CAAC,QAAQ,EAAE,GAAG,CAClB,CAAA;QACL,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,GAAG,iBAAiB,CAAC,MAAM,kDAAkD,CAChF,CAAA;QAED,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YAC7B,6DAA6D;YAC7D,0EAA0E;YAC1E,oEAAoE;YACpE,+DAA+D;YAC/D,8DAA8D;YAC9D,MAAM,mCAAmC,GACrC,iBAAiB,CAAC,MAAM,CACpB,CAAC,SAAS,EAAE,EAAE,CACV,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,WAAW,KAAK,SAAS,CAAC,CACvD,CAAA;YAEL,IAAI,mCAAmC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjD,MAAM,KAAK,GAAG,IAAI,qCAAqC,CACnD,mCAAmC,CACtC,CAAA;gBACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAC/B,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAC/C,CAAA;gBACD,MAAM,KAAK,CAAA;YACf,CAAC;QACL,CAAC;QAED,0DAA0D;QAC1D,oEAAoE;QAEpE,yEAAyE;QACzE,6EAA6E;QAC7E,4EAA4E;QAC5E,yEAAyE;QAEzE,MAAM,aAAa,GAAG;YAClB,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,KAAK;YACX,GAAG,EAAE,KAAK;SACb,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAEnB,KAAK,MAAM,SAAS,IAAI,iBAAiB,EAAE,CAAC;YACxC,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACrB,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAA;gBAEjD,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;oBAC3B,SAAS,CAAC,WAAW,GAAG,aAAa,CAAA;gBACzC,CAAC;qBAAM,CAAC;oBACJ,SAAS,CAAC,WAAW,GAAG,UAAU,CAAA;gBACtC,CAAC;YACL,CAAC;QACL,CAAC;QAED,2CAA2C;QAC3C,IAAI,sBAAsB,GAAG,KAAK,CAAA;QAClC,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;YACjE,MAAM,WAAW,CAAC,eAAe,EAAE,CAAA;YACnC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;YACpC,sBAAsB,GAAG,IAAI,CAAA;QACjC,CAAC;QAED,2CAA2C;QAC3C,IAAI,CAAC;YACD,KAAK,MAAM,SAAS,IAAI,iBAAiB,EAAE,CAAC;gBACxC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;oBACZ,mEAAmE;oBACnE,MAAM,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAA;oBAE1D,4DAA4D;oBAC5D,SAAQ;gBACZ,CAAC;gBAED,IAAI,SAAS,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;oBAC5D,MAAM,WAAW,CAAC,eAAe,EAAE,CAAA;oBACnC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;oBACpC,sBAAsB,GAAG,IAAI,CAAA;gBACjC,CAAC;gBAED,MAAM,SAAS;qBACV,QAAS,CAAC,EAAE,CAAC,WAAW,CAAC;qBACzB,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBACb,0CAA0C;oBAC1C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAC/B,cAAc,SAAS,CAAC,IAAI,oBAAoB,KAAK,EAAE,OAAO,EAAE,CACnE,CAAA;oBACD,MAAM,KAAK,CAAA;gBACf,CAAC,CAAC;qBACD,IAAI,CAAC,KAAK,IAAI,EAAE;oBACb,qFAAqF;oBACrF,MAAM,IAAI,CAAC,uBAAuB,CAC9B,WAAW,EACX,SAAS,CACZ,CAAA;oBACD,sCAAsC;oBACtC,IAAI,SAAS,CAAC,WAAW,IAAI,sBAAsB,EAAE,CAAC;wBAClD,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;wBACrC,MAAM,WAAW,CAAC,cAAc,EAAE,CAAA;oBACtC,CAAC;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,GAAG,EAAE;oBACP,0CAA0C;oBAC1C,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBACjC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,aAAa,SAAS,CAAC,IAAI,aACvB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAC5B,wBAAwB,CAC3B,CAAA;gBACL,CAAC,CAAC,CAAA;YACV,CAAC;YAED,sCAAsC;YACtC,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,IAAI,sBAAsB,EAAE,CAAC;gBACvD,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;gBACrC,MAAM,WAAW,CAAC,cAAc,EAAE,CAAA;YACtC,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,2DAA2D;oBAC3D,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YAED,MAAM,GAAG,CAAA;QACb,CAAC;gBAAS,CAAC;YACP,oDAAoD;YACpD,IAAI,CAAC,IAAI,CAAC,WAAW;gBAAE,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QACtD,CAAC;QACD,OAAO,iBAAiB,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB;QACnB,MAAM,WAAW,GACb,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;QAE3D,kDAAkD;QAClD,MAAM,IAAI,CAAC,+BAA+B,CAAC,WAAW,CAAC,CAAA;QAEvD,wDAAwD;QACxD,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA;QAClE,IAAI,eAAe,CAAC,oBAAoB,CAAC,aAAa,CAAC,EAAE,CAAC;YACtD,MAAM,aAAa,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAA;QACnE,CAAC;QAED,iEAAiE;QACjE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACxD,WAAW,CACd,CAAA;QAED,gDAAgD;QAChD,MAAM,yBAAyB,GAC3B,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,CAAA;QAEvD,gEAAgE;QAChE,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAC7B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,8DAA8D,CACjE,CAAA;YACD,OAAM;QACV,CAAC;QAED,+CAA+C;QAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAE1C,uDAAuD;QACvD,MAAM,iBAAiB,GAAG,aAAa,CAAC,IAAI,CACxC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,yBAA0B,CAAC,IAAI,CACpE,CAAA;QAED,gEAAgE;QAChE,IAAI,CAAC,iBAAiB;YAClB,MAAM,IAAI,YAAY,CAClB,gBAAgB,yBAAyB,CAAC,IAAI,+HAA+H,CAChL,CAAA;QAEL,4CAA4C;QAC5C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,GAAG,kBAAkB,CAAC,MAAM,iDAAiD,CAChF,CAAA;QACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,GACI,yBAAyB,CAAC,IAC9B,uDAAuD,IAAI,IAAI,CAC3D,yBAAyB,CAAC,SAAS,CACtC,CAAC,QAAQ,EAAE,GAAG,CAClB,CAAA;QACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAA;QAE5D,2CAA2C;QAC3C,IAAI,sBAAsB,GAAG,KAAK,CAAA;QAClC,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;YAClE,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;YACpC,sBAAsB,GAAG,IAAI,CAAA;QACjC,CAAC;QAED,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACb,MAAM,WAAW,CAAC,eAAe,EAAE,CAAA;gBACnC,MAAM,iBAAiB,CAAC,QAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBACnD,MAAM,WAAW,CAAC,cAAc,EAAE,CAAA;YACtC,CAAC;YAED,MAAM,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAA;YAClE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,aAAa,iBAAiB,CAAC,IAAI,aAC/B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAC5B,wBAAwB,CAC3B,CAAA;YAED,sCAAsC;YACtC,IAAI,sBAAsB;gBAAE,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;QACrE,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,2DAA2D;oBAC3D,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YAED,MAAM,GAAG,CAAA;QACb,CAAC;gBAAS,CAAC;YACP,oDAAoD;YACpD,IAAI,CAAC,IAAI,CAAC,WAAW;gBAAE,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QACtD,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,KAAK,CAAC,+BAA+B,CAC3C,WAAwB;QAExB,uCAAuC;QACvC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACpD,OAAM;QACV,CAAC;QACD,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA,CAAC,0CAA0C;QAC9G,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,MAAM,WAAW,CAAC,WAAW,CACzB,IAAI,KAAK,CAAC;gBACN,QAAQ,EAAE,IAAI,CAAC,kBAAkB;gBACjC,MAAM,EAAE,IAAI,CAAC,gBAAgB;gBAC7B,IAAI,EAAE,IAAI,CAAC,eAAe;gBAC1B,OAAO,EAAE;oBACL;wBACI,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;4BACvC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;iCACvC,WAAW;yBACnB,CAAC;wBACF,WAAW,EAAE,IAAI;wBACjB,kBAAkB,EAAE,WAAW;wBAC/B,SAAS,EAAE,IAAI;wBACf,UAAU,EAAE,KAAK;qBACpB;oBACD;wBACI,IAAI,EAAE,WAAW;wBACjB,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;4BACvC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;iCACvC,kBAAkB;yBAC1B,CAAC;wBACF,SAAS,EAAE,KAAK;wBAChB,UAAU,EAAE,KAAK;qBACpB;oBACD;wBACI,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;4BACvC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;iCACvC,aAAa;yBACrB,CAAC;wBACF,UAAU,EAAE,KAAK;qBACpB;iBACJ;aACJ,CAAC,CACL,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,sBAAsB,CAClC,WAAwB;QAExB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACpD,MAAM,WAAW,GAAG,WAA+B,CAAA;YACnD,OAAO,WAAW;iBACb,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;iBACpC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;iBACjB,OAAO,EAAE,CAAA;QAClB,CAAC;aAAM,CAAC;YACJ,MAAM,aAAa,GAAoB,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO;iBAC/D,kBAAkB,CAAC,WAAW,CAAC;iBAC/B,MAAM,EAAE;iBACR,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC;iBACpD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,CAAC;iBACpD,UAAU,EAAE,CAAA;YACjB,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;gBACtC,OAAO,IAAI,SAAS,CAChB,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAC5B,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,EACnC,YAAY,CAAC,MAAM,CAAC,CACvB,CAAA;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACO,aAAa;QACnB,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;YAC5D,MAAM,kBAAkB,GACpB,SAAS,CAAC,IAAI,IAAK,SAAS,CAAC,WAAmB,CAAC,IAAI,CAAA;YACzD,MAAM,kBAAkB,GAAG,QAAQ,CAC/B,kBAAkB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAC9B,EAAE,CACL,CAAA;YACD,IAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBACnD,MAAM,IAAI,YAAY,CAClB,GAAG,kBAAkB,6FAA6F,CACrH,CAAA;YACL,CAAC;YAED,OAAO,IAAI,SAAS,CAChB,SAAS,EACT,kBAAkB,EAClB,kBAAkB,EAClB,SAAS,CACZ,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAA;QAE5C,yBAAyB;QACzB,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAA;IAC/D,CAAC;IAES,2BAA2B,CAAC,UAAuB;QACzD,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QACpE,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CACzB,IAAI,GAAG,CACH,cAAc,CAAC,MAAM,CACjB,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE,CACrB,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,KAAK,CACpD,CACJ,CACJ,CAAA;QACD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,KAAK,CAAC,yBAAyB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACjE,CAAC;IACL,CAAC;IAED;;OAEG;IACO,2BAA2B,CACjC,UAAuB;QAEvB,MAAM,gBAAgB,GAAG,UAAU;aAC9B,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC;aAC7B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;QACrD,OAAO,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;IACxE,CAAC;IAED;;;OAGG;IACO,0BAA0B,CAChC,gBAA6B;QAE7B,OAAO,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;IACxE,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CACnC,WAAwB,EACxB,SAAoB;QAEpB,MAAM,MAAM,GAAkB,EAAE,CAAA;QAChC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClD,MAAM,CAAC,WAAW,CAAC,GAAG,IAAI,cAAc,CACpC,SAAS,CAAC,SAAS,EACnB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;gBACjC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;qBACvC,kBAAkB;aAC1B,CAAQ,CACZ,CAAA;YACD,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,cAAc,CAC/B,SAAS,CAAC,IAAI,EACd,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;gBACjC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa;aAC7D,CAAQ,CACZ,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC,SAAS,CAAA;YACzC,MAAM,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,CAAA;QACnC,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACpD,MAAM,WAAW,GAAG,WAA+B,CAAA;YACnD,MAAM,WAAW,CAAC,kBAAkB;iBAC/B,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAS,CAAC;iBACpC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC;iBACpC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC1B,CAAC;aAAM,CAAC;YACJ,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAA;YACnD,MAAM,EAAE;iBACH,MAAM,EAAE;iBACR,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC;iBAC1B,MAAM,CAAC,MAAM,CAAC;iBACd,OAAO,EAAE,CAAA;QAClB,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CACnC,WAAwB,EACxB,SAAoB;QAEpB,MAAM,UAAU,GAAkB,EAAE,CAAA;QACpC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClD,UAAU,CAAC,WAAW,CAAC,GAAG,IAAI,cAAc,CACxC,SAAS,CAAC,SAAS,EACnB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;gBACjC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;qBACvC,kBAAkB;aAC1B,CAAQ,CACZ,CAAA;YACD,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,cAAc,CACnC,SAAS,CAAC,IAAI,EACd,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;gBACjC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa;aAC7D,CAAQ,CACZ,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,UAAU,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC,SAAS,CAAA;YAC7C,UAAU,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,CAAA;QACvC,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACpD,MAAM,WAAW,GAAG,WAA+B,CAAA;YACnD,MAAM,WAAW,CAAC,kBAAkB;iBAC/B,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAS,CAAC;iBACpC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC;iBACpC,SAAS,CAAC,UAAU,CAAC,CAAA;QAC9B,CAAC;aAAM,CAAC;YACJ,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAA;YACnD,MAAM,EAAE;iBACH,MAAM,EAAE;iBACR,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC;iBAC1B,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC;iBAC/C,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;iBACxC,aAAa,CAAC,UAAU,CAAC;iBACzB,OAAO,EAAE,CAAA;QAClB,CAAC;IACL,CAAC;IAES,KAAK,CAAC,eAAe,CAC3B,QAAsD;QAEtD,MAAM,WAAW,GACb,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;QAE3D,IAAI,CAAC;YACD,OAAO,MAAM,QAAQ,CAAC,WAAW,CAAC,CAAA;QACtC,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACpB,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;YAC/B,CAAC;QACL,CAAC;IACL,CAAC;CACJ", "file": "MigrationExecutor.js", "sourcesContent": ["import { Table } from \"../schema-builder/table/Table\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { Migration } from \"./Migration\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { MssqlParameter } from \"../driver/sqlserver/MssqlParameter\"\nimport { MongoQueryRunner } from \"../driver/mongodb/MongoQueryRunner\"\nimport { ForbiddenTransactionModeOverrideError, TypeORMError } from \"../error\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\n\n/**\n * Executes migrations: runs pending and reverts previously executed migrations.\n */\nexport class MigrationExecutor {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Indicates how migrations should be run in transactions.\n     *   all: all migrations are run in a single transaction\n     *   none: all migrations are run without a transaction\n     *   each: each migration is run in a separate transaction\n     */\n    transaction: \"all\" | \"none\" | \"each\" = \"all\"\n\n    /**\n     * Option to fake-run or fake-revert a migration, adding to the\n     * executed migrations table, but not actually running it. This feature is\n     * useful for when migrations are added after the fact or for\n     * interoperability between applications which are desired to each keep\n     * a consistent migration history.\n     */\n    fake: boolean\n\n    // -------------------------------------------------------------------------\n    // Private Properties\n    // -------------------------------------------------------------------------\n\n    private readonly migrationsDatabase?: string\n    private readonly migrationsSchema?: string\n    private readonly migrationsTable: string\n    private readonly migrationsTableName: string\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        protected connection: DataSource,\n        protected queryRunner?: QueryRunner,\n    ) {\n        const { schema } = this.connection.driver.options as any\n        const database = this.connection.driver.database\n        this.migrationsDatabase = database\n        this.migrationsSchema = schema\n        this.migrationsTableName =\n            connection.options.migrationsTableName || \"migrations\"\n        this.migrationsTable = this.connection.driver.buildTableName(\n            this.migrationsTableName,\n            schema,\n            database,\n        )\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Tries to execute a single migration given.\n     */\n    public async executeMigration(migration: Migration): Promise<Migration> {\n        return this.withQueryRunner(async (queryRunner) => {\n            await this.createMigrationsTableIfNotExist(queryRunner)\n\n            // create typeorm_metadata table if it's not created yet\n            const schemaBuilder = this.connection.driver.createSchemaBuilder()\n            if (InstanceChecker.isRdbmsSchemaBuilder(schemaBuilder)) {\n                await schemaBuilder.createMetadataTableIfNecessary(queryRunner)\n            }\n\n            await queryRunner.beforeMigration()\n            await (migration.instance as any).up(queryRunner)\n            await queryRunner.afterMigration()\n            await this.insertExecutedMigration(queryRunner, migration)\n\n            return migration\n        })\n    }\n\n    /**\n     * Returns an array of all migrations.\n     */\n    public async getAllMigrations(): Promise<Migration[]> {\n        return Promise.resolve(this.getMigrations())\n    }\n\n    /**\n     * Returns an array of all executed migrations.\n     */\n    public async getExecutedMigrations(): Promise<Migration[]> {\n        return this.withQueryRunner(async (queryRunner) => {\n            await this.createMigrationsTableIfNotExist(queryRunner)\n\n            return await this.loadExecutedMigrations(queryRunner)\n        })\n    }\n\n    /**\n     * Returns an array of all pending migrations.\n     */\n    public async getPendingMigrations(): Promise<Migration[]> {\n        const allMigrations = await this.getAllMigrations()\n        const executedMigrations = await this.getExecutedMigrations()\n\n        return allMigrations.filter(\n            (migration) =>\n                !executedMigrations.find(\n                    (executedMigration) =>\n                        executedMigration.name === migration.name,\n                ),\n        )\n    }\n\n    /**\n     * Inserts an executed migration.\n     */\n    public insertMigration(migration: Migration): Promise<void> {\n        return this.withQueryRunner((q) =>\n            this.insertExecutedMigration(q, migration),\n        )\n    }\n\n    /**\n     * Deletes an executed migration.\n     */\n    public deleteMigration(migration: Migration): Promise<void> {\n        return this.withQueryRunner((q) =>\n            this.deleteExecutedMigration(q, migration),\n        )\n    }\n\n    /**\n     * Lists all migrations and whether they have been executed or not\n     * returns true if there are unapplied migrations\n     */\n    async showMigrations(): Promise<boolean> {\n        let hasUnappliedMigrations = false\n        const queryRunner =\n            this.queryRunner || this.connection.createQueryRunner()\n        // create migrations table if its not created yet\n        await this.createMigrationsTableIfNotExist(queryRunner)\n\n        // get all migrations that are executed and saved in the database\n        const executedMigrations = await this.loadExecutedMigrations(\n            queryRunner,\n        )\n\n        // get all user's migrations in the source code\n        const allMigrations = this.getMigrations()\n\n        for (const migration of allMigrations) {\n            const executedMigration = executedMigrations.find(\n                (executedMigration) =>\n                    executedMigration.name === migration.name,\n            )\n\n            if (executedMigration) {\n                this.connection.logger.logSchemaBuild(\n                    `[X] ${executedMigration.id} ${migration.name}`,\n                )\n            } else {\n                hasUnappliedMigrations = true\n                this.connection.logger.logSchemaBuild(`[ ] ${migration.name}`)\n            }\n        }\n\n        // if query runner was created by us then release it\n        if (!this.queryRunner) {\n            await queryRunner.release()\n        }\n\n        return hasUnappliedMigrations\n    }\n\n    /**\n     * Executes all pending migrations. Pending migrations are migrations that are not yet executed,\n     * thus not saved in the database.\n     */\n    async executePendingMigrations(): Promise<Migration[]> {\n        const queryRunner =\n            this.queryRunner || this.connection.createQueryRunner()\n        // create migrations table if it's not created yet\n        await this.createMigrationsTableIfNotExist(queryRunner)\n\n        // create the typeorm_metadata table if it's not created yet\n        const schemaBuilder = this.connection.driver.createSchemaBuilder()\n        if (InstanceChecker.isRdbmsSchemaBuilder(schemaBuilder)) {\n            await schemaBuilder.createMetadataTableIfNecessary(queryRunner)\n        }\n\n        // get all migrations that are executed and saved in the database\n        const executedMigrations = await this.loadExecutedMigrations(\n            queryRunner,\n        )\n\n        // get the time when last migration was executed\n        const lastTimeExecutedMigration =\n            this.getLatestTimestampMigration(executedMigrations)\n\n        // get all user's migrations in the source code\n        const allMigrations = this.getMigrations()\n\n        // variable to store all migrations we did successfully\n        const successMigrations: Migration[] = []\n\n        // find all migrations that needs to be executed\n        const pendingMigrations = allMigrations.filter((migration) => {\n            // check if we already have executed migration\n            const executedMigration = executedMigrations.find(\n                (executedMigration) =>\n                    executedMigration.name === migration.name,\n            )\n            if (executedMigration) return false\n\n            // migration is new and not executed. now check if its timestamp is correct\n            // if (lastTimeExecutedMigration && migration.timestamp < lastTimeExecutedMigration.timestamp)\n            //     throw new TypeORMError(`New migration found: ${migration.name}, however this migration's timestamp is not valid. Migration's timestamp should not be older then migrations already executed in the database.`);\n\n            // every check is passed means that migration was not run yet and we need to run it\n            return true\n        })\n\n        // if no migrations are pending then nothing to do here\n        if (!pendingMigrations.length) {\n            this.connection.logger.logSchemaBuild(`No migrations are pending`)\n            // if query runner was created by us then release it\n            if (!this.queryRunner) await queryRunner.release()\n            return []\n        }\n\n        // log information about migration execution\n        this.connection.logger.logSchemaBuild(\n            `${executedMigrations.length} migrations are already loaded in the database.`,\n        )\n        this.connection.logger.logSchemaBuild(\n            `${allMigrations.length} migrations were found in the source code.`,\n        )\n        if (lastTimeExecutedMigration)\n            this.connection.logger.logSchemaBuild(\n                `${\n                    lastTimeExecutedMigration.name\n                } is the last executed migration. It was executed on ${new Date(\n                    lastTimeExecutedMigration.timestamp,\n                ).toString()}.`,\n            )\n        this.connection.logger.logSchemaBuild(\n            `${pendingMigrations.length} migrations are new migrations must be executed.`,\n        )\n\n        if (this.transaction === \"all\") {\n            // If we desire to run all migrations in a single transaction\n            // but there is a migration that explicitly overrides the transaction mode\n            // then we have to fail since we cannot properly resolve that intent\n            // In theory we could support overrides that are set to `true`,\n            // however to keep the interface more rigid, we fail those too\n            const migrationsOverridingTransactionMode =\n                pendingMigrations.filter(\n                    (migration) =>\n                        !(migration.instance?.transaction === undefined),\n                )\n\n            if (migrationsOverridingTransactionMode.length > 0) {\n                const error = new ForbiddenTransactionModeOverrideError(\n                    migrationsOverridingTransactionMode,\n                )\n                this.connection.logger.logMigration(\n                    `Migrations failed, error: ${error.message}`,\n                )\n                throw error\n            }\n        }\n\n        // Set the per-migration defaults for the transaction mode\n        // so that we have one centralized place that controls this behavior\n\n        // When transaction mode is `each` the default is to run in a transaction\n        // When transaction mode is `none` the default is to not run in a transaction\n        // When transaction mode is `all` the default is to not run in a transaction\n        // since all the migrations are already running in one single transaction\n\n        const txModeDefault = {\n            each: true,\n            none: false,\n            all: false,\n        }[this.transaction]\n\n        for (const migration of pendingMigrations) {\n            if (migration.instance) {\n                const instanceTx = migration.instance.transaction\n\n                if (instanceTx === undefined) {\n                    migration.transaction = txModeDefault\n                } else {\n                    migration.transaction = instanceTx\n                }\n            }\n        }\n\n        // start transaction if its not started yet\n        let transactionStartedByUs = false\n        if (this.transaction === \"all\" && !queryRunner.isTransactionActive) {\n            await queryRunner.beforeMigration()\n            await queryRunner.startTransaction()\n            transactionStartedByUs = true\n        }\n\n        // run all pending migrations in a sequence\n        try {\n            for (const migration of pendingMigrations) {\n                if (this.fake) {\n                    // directly insert migration record into the database if it is fake\n                    await this.insertExecutedMigration(queryRunner, migration)\n\n                    // nothing else needs to be done, continue to next migration\n                    continue\n                }\n\n                if (migration.transaction && !queryRunner.isTransactionActive) {\n                    await queryRunner.beforeMigration()\n                    await queryRunner.startTransaction()\n                    transactionStartedByUs = true\n                }\n\n                await migration\n                    .instance!.up(queryRunner)\n                    .catch((error) => {\n                        // informative log about migration failure\n                        this.connection.logger.logMigration(\n                            `Migration \"${migration.name}\" failed, error: ${error?.message}`,\n                        )\n                        throw error\n                    })\n                    .then(async () => {\n                        // now when migration is executed we need to insert record about it into the database\n                        await this.insertExecutedMigration(\n                            queryRunner,\n                            migration,\n                        )\n                        // commit transaction if we started it\n                        if (migration.transaction && transactionStartedByUs) {\n                            await queryRunner.commitTransaction()\n                            await queryRunner.afterMigration()\n                        }\n                    })\n                    .then(() => {\n                        // informative log about migration success\n                        successMigrations.push(migration)\n                        this.connection.logger.logSchemaBuild(\n                            `Migration ${migration.name} has been ${\n                                this.fake ? \"(fake) \" : \"\"\n                            }executed successfully.`,\n                        )\n                    })\n            }\n\n            // commit transaction if we started it\n            if (this.transaction === \"all\" && transactionStartedByUs) {\n                await queryRunner.commitTransaction()\n                await queryRunner.afterMigration()\n            }\n        } catch (err) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    // we throw original error even if rollback thrown an error\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n\n            throw err\n        } finally {\n            // if query runner was created by us then release it\n            if (!this.queryRunner) await queryRunner.release()\n        }\n        return successMigrations\n    }\n\n    /**\n     * Reverts last migration that were run.\n     */\n    async undoLastMigration(): Promise<void> {\n        const queryRunner =\n            this.queryRunner || this.connection.createQueryRunner()\n\n        // create migrations table if it's not created yet\n        await this.createMigrationsTableIfNotExist(queryRunner)\n\n        // create typeorm_metadata table if it's not created yet\n        const schemaBuilder = this.connection.driver.createSchemaBuilder()\n        if (InstanceChecker.isRdbmsSchemaBuilder(schemaBuilder)) {\n            await schemaBuilder.createMetadataTableIfNecessary(queryRunner)\n        }\n\n        // get all migrations that are executed and saved in the database\n        const executedMigrations = await this.loadExecutedMigrations(\n            queryRunner,\n        )\n\n        // get the time when last migration was executed\n        const lastTimeExecutedMigration =\n            this.getLatestExecutedMigration(executedMigrations)\n\n        // if no migrations found in the database then nothing to revert\n        if (!lastTimeExecutedMigration) {\n            this.connection.logger.logSchemaBuild(\n                `No migrations were found in the database. Nothing to revert!`,\n            )\n            return\n        }\n\n        // get all user's migrations in the source code\n        const allMigrations = this.getMigrations()\n\n        // find the instance of the migration we need to remove\n        const migrationToRevert = allMigrations.find(\n            (migration) => migration.name === lastTimeExecutedMigration!.name,\n        )\n\n        // if no migrations found in the database then nothing to revert\n        if (!migrationToRevert)\n            throw new TypeORMError(\n                `No migration ${lastTimeExecutedMigration.name} was found in the source code. Make sure you have this migration in your codebase and its included in the connection options.`,\n            )\n\n        // log information about migration execution\n        this.connection.logger.logSchemaBuild(\n            `${executedMigrations.length} migrations are already loaded in the database.`,\n        )\n        this.connection.logger.logSchemaBuild(\n            `${\n                lastTimeExecutedMigration.name\n            } is the last executed migration. It was executed on ${new Date(\n                lastTimeExecutedMigration.timestamp,\n            ).toString()}.`,\n        )\n        this.connection.logger.logSchemaBuild(`Now reverting it...`)\n\n        // start transaction if its not started yet\n        let transactionStartedByUs = false\n        if (this.transaction !== \"none\" && !queryRunner.isTransactionActive) {\n            await queryRunner.startTransaction()\n            transactionStartedByUs = true\n        }\n\n        try {\n            if (!this.fake) {\n                await queryRunner.beforeMigration()\n                await migrationToRevert.instance!.down(queryRunner)\n                await queryRunner.afterMigration()\n            }\n\n            await this.deleteExecutedMigration(queryRunner, migrationToRevert)\n            this.connection.logger.logSchemaBuild(\n                `Migration ${migrationToRevert.name} has been ${\n                    this.fake ? \"(fake) \" : \"\"\n                }reverted successfully.`,\n            )\n\n            // commit transaction if we started it\n            if (transactionStartedByUs) await queryRunner.commitTransaction()\n        } catch (err) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    // we throw original error even if rollback thrown an error\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n\n            throw err\n        } finally {\n            // if query runner was created by us then release it\n            if (!this.queryRunner) await queryRunner.release()\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates table \"migrations\" that will store information about executed migrations.\n     */\n    protected async createMigrationsTableIfNotExist(\n        queryRunner: QueryRunner,\n    ): Promise<void> {\n        // If driver is mongo no need to create\n        if (this.connection.driver.options.type === \"mongodb\") {\n            return\n        }\n        const tableExist = await queryRunner.hasTable(this.migrationsTable) // todo: table name should be configurable\n        if (!tableExist) {\n            await queryRunner.createTable(\n                new Table({\n                    database: this.migrationsDatabase,\n                    schema: this.migrationsSchema,\n                    name: this.migrationsTable,\n                    columns: [\n                        {\n                            name: \"id\",\n                            type: this.connection.driver.normalizeType({\n                                type: this.connection.driver.mappedDataTypes\n                                    .migrationId,\n                            }),\n                            isGenerated: true,\n                            generationStrategy: \"increment\",\n                            isPrimary: true,\n                            isNullable: false,\n                        },\n                        {\n                            name: \"timestamp\",\n                            type: this.connection.driver.normalizeType({\n                                type: this.connection.driver.mappedDataTypes\n                                    .migrationTimestamp,\n                            }),\n                            isPrimary: false,\n                            isNullable: false,\n                        },\n                        {\n                            name: \"name\",\n                            type: this.connection.driver.normalizeType({\n                                type: this.connection.driver.mappedDataTypes\n                                    .migrationName,\n                            }),\n                            isNullable: false,\n                        },\n                    ],\n                }),\n            )\n        }\n    }\n\n    /**\n     * Loads all migrations that were executed and saved into the database (sorts by id).\n     */\n    protected async loadExecutedMigrations(\n        queryRunner: QueryRunner,\n    ): Promise<Migration[]> {\n        if (this.connection.driver.options.type === \"mongodb\") {\n            const mongoRunner = queryRunner as MongoQueryRunner\n            return mongoRunner\n                .cursor(this.migrationsTableName, {})\n                .sort({ _id: -1 })\n                .toArray()\n        } else {\n            const migrationsRaw: ObjectLiteral[] = await this.connection.manager\n                .createQueryBuilder(queryRunner)\n                .select()\n                .orderBy(this.connection.driver.escape(\"id\"), \"DESC\")\n                .from(this.migrationsTable, this.migrationsTableName)\n                .getRawMany()\n            return migrationsRaw.map((migrationRaw) => {\n                return new Migration(\n                    parseInt(migrationRaw[\"id\"]),\n                    parseInt(migrationRaw[\"timestamp\"]),\n                    migrationRaw[\"name\"],\n                )\n            })\n        }\n    }\n\n    /**\n     * Gets all migrations that setup for this connection.\n     */\n    protected getMigrations(): Migration[] {\n        const migrations = this.connection.migrations.map((migration) => {\n            const migrationClassName =\n                migration.name || (migration.constructor as any).name\n            const migrationTimestamp = parseInt(\n                migrationClassName.substr(-13),\n                10,\n            )\n            if (!migrationTimestamp || isNaN(migrationTimestamp)) {\n                throw new TypeORMError(\n                    `${migrationClassName} migration name is wrong. Migration class name should have a JavaScript timestamp appended.`,\n                )\n            }\n\n            return new Migration(\n                undefined,\n                migrationTimestamp,\n                migrationClassName,\n                migration,\n            )\n        })\n\n        this.checkForDuplicateMigrations(migrations)\n\n        // sort them by timestamp\n        return migrations.sort((a, b) => a.timestamp - b.timestamp)\n    }\n\n    protected checkForDuplicateMigrations(migrations: Migration[]) {\n        const migrationNames = migrations.map((migration) => migration.name)\n        const duplicates = Array.from(\n            new Set(\n                migrationNames.filter(\n                    (migrationName, index) =>\n                        migrationNames.indexOf(migrationName) < index,\n                ),\n            ),\n        )\n        if (duplicates.length > 0) {\n            throw Error(`Duplicate migrations: ${duplicates.join(\", \")}`)\n        }\n    }\n\n    /**\n     * Finds the latest migration (sorts by timestamp) in the given array of migrations.\n     */\n    protected getLatestTimestampMigration(\n        migrations: Migration[],\n    ): Migration | undefined {\n        const sortedMigrations = migrations\n            .map((migration) => migration)\n            .sort((a, b) => (a.timestamp - b.timestamp) * -1)\n        return sortedMigrations.length > 0 ? sortedMigrations[0] : undefined\n    }\n\n    /**\n     * Finds the latest migration in the given array of migrations.\n     * PRE: Migration array must be sorted by descending id.\n     */\n    protected getLatestExecutedMigration(\n        sortedMigrations: Migration[],\n    ): Migration | undefined {\n        return sortedMigrations.length > 0 ? sortedMigrations[0] : undefined\n    }\n\n    /**\n     * Inserts new executed migration's data into migrations table.\n     */\n    protected async insertExecutedMigration(\n        queryRunner: QueryRunner,\n        migration: Migration,\n    ): Promise<void> {\n        const values: ObjectLiteral = {}\n        if (this.connection.driver.options.type === \"mssql\") {\n            values[\"timestamp\"] = new MssqlParameter(\n                migration.timestamp,\n                this.connection.driver.normalizeType({\n                    type: this.connection.driver.mappedDataTypes\n                        .migrationTimestamp,\n                }) as any,\n            )\n            values[\"name\"] = new MssqlParameter(\n                migration.name,\n                this.connection.driver.normalizeType({\n                    type: this.connection.driver.mappedDataTypes.migrationName,\n                }) as any,\n            )\n        } else {\n            values[\"timestamp\"] = migration.timestamp\n            values[\"name\"] = migration.name\n        }\n        if (this.connection.driver.options.type === \"mongodb\") {\n            const mongoRunner = queryRunner as MongoQueryRunner\n            await mongoRunner.databaseConnection\n                .db(this.connection.driver.database!)\n                .collection(this.migrationsTableName)\n                .insertOne(values)\n        } else {\n            const qb = queryRunner.manager.createQueryBuilder()\n            await qb\n                .insert()\n                .into(this.migrationsTable)\n                .values(values)\n                .execute()\n        }\n    }\n\n    /**\n     * Delete previously executed migration's data from the migrations table.\n     */\n    protected async deleteExecutedMigration(\n        queryRunner: QueryRunner,\n        migration: Migration,\n    ): Promise<void> {\n        const conditions: ObjectLiteral = {}\n        if (this.connection.driver.options.type === \"mssql\") {\n            conditions[\"timestamp\"] = new MssqlParameter(\n                migration.timestamp,\n                this.connection.driver.normalizeType({\n                    type: this.connection.driver.mappedDataTypes\n                        .migrationTimestamp,\n                }) as any,\n            )\n            conditions[\"name\"] = new MssqlParameter(\n                migration.name,\n                this.connection.driver.normalizeType({\n                    type: this.connection.driver.mappedDataTypes.migrationName,\n                }) as any,\n            )\n        } else {\n            conditions[\"timestamp\"] = migration.timestamp\n            conditions[\"name\"] = migration.name\n        }\n\n        if (this.connection.driver.options.type === \"mongodb\") {\n            const mongoRunner = queryRunner as MongoQueryRunner\n            await mongoRunner.databaseConnection\n                .db(this.connection.driver.database!)\n                .collection(this.migrationsTableName)\n                .deleteOne(conditions)\n        } else {\n            const qb = queryRunner.manager.createQueryBuilder()\n            await qb\n                .delete()\n                .from(this.migrationsTable)\n                .where(`${qb.escape(\"timestamp\")} = :timestamp`)\n                .andWhere(`${qb.escape(\"name\")} = :name`)\n                .setParameters(conditions)\n                .execute()\n        }\n    }\n\n    protected async withQueryRunner<T extends any>(\n        callback: (queryRunner: QueryRunner) => T | Promise<T>,\n    ) {\n        const queryRunner =\n            this.queryRunner || this.connection.createQueryRunner()\n\n        try {\n            return await callback(queryRunner)\n        } finally {\n            if (!this.queryRunner) {\n                await queryRunner.release()\n            }\n        }\n    }\n}\n"], "sourceRoot": ".."}