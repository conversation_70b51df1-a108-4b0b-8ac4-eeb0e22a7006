{"version": 3, "sources": ["../../src/query-runner/QueryResult.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,MAAa,WAAW;IAAxB;QAMI;;WAEG;QACH,YAAO,GAAQ,EAAE,CAAA;IAMrB,CAAC;CAAA;AAfD,kCAeC", "file": "QueryResult.js", "sourcesContent": ["/**\n * Result object returned by UpdateQueryBuilder execution.\n */\nexport class QueryResult<T = any> {\n    /**\n     * Raw SQL result returned by executed query.\n     */\n    raw: any\n\n    /**\n     * Rows\n     */\n    records: T[] = []\n\n    /**\n     * Number of affected rows/documents\n     */\n    affected?: number\n}\n"], "sourceRoot": ".."}