{"version": 3, "sources": ["../../src/schema-builder/table/TableExclusion.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACH,MAAa,cAAc;IAiBvB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,OAA8B;QApBjC,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;QAqBjD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACxB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAA;IACxC,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK;QACD,OAAO,IAAI,cAAc,CAAwB;YAC7C,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;SAC9B,CAAC,CAAA;IACN,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,iBAAoC;QAC9C,OAAO,IAAI,cAAc,CAAwB;YAC7C,IAAI,EAAE,iBAAiB,CAAC,IAAI;YAC5B,UAAU,EAAE,iBAAiB,CAAC,UAAU;SAC3C,CAAC,CAAA;IACN,CAAC;CACJ;AArDD,wCAqDC", "file": "TableExclusion.js", "sourcesContent": ["import { TableExclusionOptions } from \"../options/TableExclusionOptions\"\nimport { ExclusionMetadata } from \"../../metadata/ExclusionMetadata\"\n\n/**\n * Database's table exclusion constraint stored in this class.\n */\nexport class TableExclusion {\n    readonly \"@instanceof\" = Symbol.for(\"TableExclusion\")\n\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Constraint name.\n     */\n    name?: string\n\n    /**\n     * Exclusion expression.\n     */\n    expression?: string\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(options: TableExclusionOptions) {\n        this.name = options.name\n        this.expression = options.expression\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a new copy of this constraint with exactly same properties.\n     */\n    clone(): TableExclusion {\n        return new TableExclusion(<TableExclusionOptions>{\n            name: this.name,\n            expression: this.expression,\n        })\n    }\n\n    // -------------------------------------------------------------------------\n    // Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates exclusions from the exclusion metadata object.\n     */\n    static create(exclusionMetadata: ExclusionMetadata): TableExclusion {\n        return new TableExclusion(<TableExclusionOptions>{\n            name: exclusionMetadata.name,\n            expression: exclusionMetadata.expression,\n        })\n    }\n}\n"], "sourceRoot": "../.."}