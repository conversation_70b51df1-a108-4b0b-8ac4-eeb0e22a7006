{"version": 3, "sources": ["../../src/schema-builder/table/TableCheck.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACH,MAAa,UAAU;IAsBnB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,OAA0B;QAzB7B,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;QAWjD;;WAEG;QACH,gBAAW,GAAc,EAAE,CAAA;QAYvB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACxB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QACtC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAA;IACxC,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK;QACD,OAAO,IAAI,UAAU,CAAoB;YACrC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1D,UAAU,EAAE,IAAI,CAAC,UAAU;SAC9B,CAAC,CAAA;IACN,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,aAA4B;QACtC,OAAO,IAAI,UAAU,CAAoB;YACrC,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,UAAU,EAAE,aAAa,CAAC,UAAU;SACvC,CAAC,CAAA;IACN,CAAC;CACJ;AA5DD,gCA4DC", "file": "TableCheck.js", "sourcesContent": ["import { TableCheckOptions } from \"../options/TableCheckOptions\"\nimport { CheckMetadata } from \"../../metadata/CheckMetadata\"\n\n/**\n * Database's table check constraint stored in this class.\n */\nexport class TableCheck {\n    readonly \"@instanceof\" = Symbol.for(\"TableCheck\")\n\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Constraint name.\n     */\n    name?: string\n\n    /**\n     * Column that contains this constraint.\n     */\n    columnNames?: string[] = []\n\n    /**\n     * Check expression.\n     */\n    expression?: string\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(options: TableCheckOptions) {\n        this.name = options.name\n        this.columnNames = options.columnNames\n        this.expression = options.expression\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a new copy of this constraint with exactly same properties.\n     */\n    clone(): TableCheck {\n        return new TableCheck(<TableCheckOptions>{\n            name: this.name,\n            columnNames: this.columnNames ? [...this.columnNames] : [],\n            expression: this.expression,\n        })\n    }\n\n    // -------------------------------------------------------------------------\n    // Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates checks from the check metadata object.\n     */\n    static create(checkMetadata: CheckMetadata): TableCheck {\n        return new TableCheck(<TableCheckOptions>{\n            name: checkMetadata.name,\n            expression: checkMetadata.expression,\n        })\n    }\n}\n"], "sourceRoot": "../.."}