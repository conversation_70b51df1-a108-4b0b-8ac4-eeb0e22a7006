{"version": 3, "sources": ["../../src/schema-builder/table/Table.ts"], "names": [], "mappings": ";;;AAAA,+CAA2C;AAC3C,6CAAyC;AACzC,uDAAmD;AAInD,mDAA+C;AAC/C,+CAA2C;AAC3C,6CAAyC;AACzC,qDAAiD;AAEjD;;GAEG;AACH,MAAa,KAAK;IA4Ed,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,OAAsB;QA/EzB,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAuB5C;;WAEG;QACH,YAAO,GAAkB,EAAE,CAAA;QAE3B;;WAEG;QACH,YAAO,GAAiB,EAAE,CAAA;QAE1B;;WAEG;QACH,gBAAW,GAAsB,EAAE,CAAA;QAEnC;;WAEG;QACH,YAAO,GAAkB,EAAE,CAAA;QAE3B;;WAEG;QACH,WAAM,GAAiB,EAAE,CAAA;QAEzB;;WAEG;QACH,eAAU,GAAqB,EAAE,CAAA;QAEjC;;;;WAIG;QACH,gBAAW,GAAY,KAAK,CAAA;QAE5B;;WAEG;QACH,iBAAY,GAAa,KAAK,CAAA;QAiB1B,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;YAChC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;YAC5B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;YAExB,IAAI,OAAO,CAAC,OAAO;gBACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAC9B,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,yBAAW,CAAC,MAAM,CAAC,CACtC,CAAA;YAEL,IAAI,OAAO,CAAC,OAAO;gBACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAC9B,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,uBAAU,CAAC,KAAK,CAAC,CACnC,CAAA;YAEL,IAAI,OAAO,CAAC,WAAW;gBACnB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CACtC,CAAC,UAAU,EAAE,EAAE,CACX,IAAI,iCAAe,CAAC;oBAChB,GAAG,UAAU;oBACb,kBAAkB,EACd,UAAU,EAAE,kBAAkB;wBAC9B,OAAO,CAAC,QAAQ;oBACpB,gBAAgB,EACZ,UAAU,EAAE,gBAAgB,IAAI,OAAO,CAAC,MAAM;iBACrD,CAAC,CACT,CAAA;YAEL,IAAI,OAAO,CAAC,OAAO;gBACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAC9B,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,yBAAW,CAAC,MAAM,CAAC,CACtC,CAAA;YAEL,IAAI,OAAO,CAAC,MAAM;gBACd,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAC5B,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,uBAAU,CAAC,KAAK,CAAC,CACnC,CAAA;YAEL,IAAI,OAAO,CAAC,UAAU;gBAClB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CACpC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,+BAAc,CAAC,SAAS,CAAC,CAC/C,CAAA;YAEL,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS;gBACjC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;YAE1C,IAAI,OAAO,CAAC,YAAY;gBAAE,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAA;YAElE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;YAE5B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QAClC,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,YAAY;IACZ,4EAA4E;IAE5E,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;IAC5D,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK;QACD,OAAO,IAAI,KAAK,CAAC;YACb,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACrD,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC7D,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAC7C,UAAU,CAAC,KAAK,EAAE,CACrB;YACD,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC7D,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC3D,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACnE,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,IAAI,CAAC,OAAO;SACxB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,MAAmB;QACzB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAC7B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAmB;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,CAAA;QACpE,IAAI,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAA;IACjE,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,gBAA6B;QAC7C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACnC,IAAI,gBAAgB,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAClC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAC9D,CAAA;YACD,IAAI,YAAY;gBAAE,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAA;QAClD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,aAA0B;QAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CACjC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,CACjD,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAA;YACzD,IAAI,WAAW,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAClC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,CACzD,CAAA;gBACD,IAAI,YAAY;oBAAE,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAA;YACnD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,eAA2B;QAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,YAAwB;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAC/B,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,CAC9C,CAAA;QACD,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAA;QAC1D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,mBAAmC;QACtD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,gBAAgC;QACtD,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CACvC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,gBAAgB,CAAC,IAAI,CAC1D,CAAA;QACD,IAAI,cAAc,EAAE,CAAC;YACjB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAA;QACtE,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAA2B;QACrC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,iBAAkC;QAC/C,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAC5B,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,KAAK,iBAAiB,CAAC,IAAI,CAC7D,CAAA;QACD,IAAI,EAAE;YAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,KAAiB,EAAE,UAAmB,KAAK;QAChD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAExB,oEAAoE;QACpE,6EAA6E;QAC7E,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,IAAI,OAAO,EAAE,CAAC;YAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAC5B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CACzC,CAAA;YACD,IAAI,MAAM;gBAAE,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAA;QACtC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,UAAsB,EAAE,UAAmB,KAAK;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAC3B,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAC5C,CAAA;QACD,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;YAEnD,oEAAoE;YACpE,0FAA0F;YAC1F,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,IAAI,OAAO,EAAE,CAAC;gBAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAC5B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CACzC,CAAA;gBACD,IAAI,MAAM;oBACN,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAC/B,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;wBAC5B,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI;wBAClC,CAAC,CAAC,KAAK,CAAC,QAAQ,CACvB,CAAA;YACT,CAAC;QACL,CAAC;IACL,CAAC;IAED,gBAAgB,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAA;IAC9D,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAmB;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;YACjC,OAAO,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAC3B,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAC7C,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,MAAmB;QACrC,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE;YAC1C,OAAO,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAChC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAC7C,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAmB;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YAClC,OAAO,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAC5B,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAC7C,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAmB;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;YAChC,OAAO,CAAC,CAAC,KAAK,CAAC,WAAY,CAAC,IAAI,CAC5B,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAC7C,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,cAA8B,EAAE,MAAc;QACxD,MAAM,QAAQ,GACV,cAAc,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ;YACvC,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAA;QACjC,MAAM,MAAM,GACR,cAAc,CAAC,MAAM,KAAM,MAAM,CAAC,OAAe,CAAC,MAAM;YACpD,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,cAAc,CAAC,MAAM,CAAA;QAE/B,MAAM,OAAO,GAAiB;YAC1B,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,MAAM,EAAE,cAAc,CAAC,MAAM;YAC7B,IAAI,EAAE,MAAM,CAAC,cAAc,CACvB,cAAc,CAAC,SAAS,EACxB,MAAM,EACN,QAAQ,CACX;YACD,YAAY,EAAE,cAAc,CAAC,YAAY;YACzC,MAAM,EAAE,cAAc,CAAC,MAAM;YAC7B,OAAO,EAAE,cAAc,CAAC,OAAO;iBAC1B,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;iBACvD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACZ,uBAAU,CAAC,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,CACtD;YACL,OAAO,EAAE,cAAc,CAAC,OAAO;iBAC1B,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,KAAK,IAAI,CAAC;iBAC7C,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,uBAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC7C,OAAO,EAAE,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAC3C,yBAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAC7B;YACD,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACxC,uBAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAC3B;YACD,UAAU,EAAE,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CACpD,+BAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CACnC;YACD,OAAO,EAAE,cAAc,CAAC,OAAO;SAClC,CAAA;QAED,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,CAAA;IAC7B,CAAC;CACJ;AA5ZD,sBA4ZC", "file": "Table.js", "sourcesContent": ["import { TableColumn } from \"./TableColumn\"\nimport { TableIndex } from \"./TableIndex\"\nimport { TableForeignKey } from \"./TableForeignKey\"\nimport { Driver } from \"../../driver/Driver\"\nimport { TableOptions } from \"../options/TableOptions\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { TableUtils } from \"../util/TableUtils\"\nimport { TableUnique } from \"./TableUnique\"\nimport { TableCheck } from \"./TableCheck\"\nimport { TableExclusion } from \"./TableExclusion\"\n\n/**\n * Table in the database represented in this class.\n */\nexport class Table {\n    readonly \"@instanceof\" = Symbol.for(\"Table\")\n\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database name that this table resides in if it applies.\n     */\n    database?: string\n\n    /**\n     * Schema name that this table resides in if it applies.\n     */\n    schema?: string\n\n    /**\n     * May contain database name, schema name and table name, unless they're the current database.\n     *\n     * E.g. myDB.mySchema.myTable\n     */\n    name: string\n\n    /**\n     * Table columns.\n     */\n    columns: TableColumn[] = []\n\n    /**\n     * Table indices.\n     */\n    indices: TableIndex[] = []\n\n    /**\n     * Table foreign keys.\n     */\n    foreignKeys: TableForeignKey[] = []\n\n    /**\n     * Table unique constraints.\n     */\n    uniques: TableUnique[] = []\n\n    /**\n     * Table check constraints.\n     */\n    checks: TableCheck[] = []\n\n    /**\n     * Table exclusion constraints.\n     */\n    exclusions: TableExclusion[] = []\n\n    /**\n     * Indicates if table was just created.\n     * This is needed, for example to check if we need to skip primary keys creation\n     * for new tables.\n     */\n    justCreated: boolean = false\n\n    /**\n     * Enables Sqlite \"WITHOUT ROWID\" modifier for the \"CREATE TABLE\" statement\n     */\n    withoutRowid?: boolean = false\n\n    /**\n     * Table engine.\n     */\n    engine?: string\n\n    /**\n     * Table comment. Not supported by all database types.\n     */\n    comment?: string\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(options?: TableOptions) {\n        if (options) {\n            this.database = options.database\n            this.schema = options.schema\n            this.name = options.name\n\n            if (options.columns)\n                this.columns = options.columns.map(\n                    (column) => new TableColumn(column),\n                )\n\n            if (options.indices)\n                this.indices = options.indices.map(\n                    (index) => new TableIndex(index),\n                )\n\n            if (options.foreignKeys)\n                this.foreignKeys = options.foreignKeys.map(\n                    (foreignKey) =>\n                        new TableForeignKey({\n                            ...foreignKey,\n                            referencedDatabase:\n                                foreignKey?.referencedDatabase ||\n                                options.database,\n                            referencedSchema:\n                                foreignKey?.referencedSchema || options.schema,\n                        }),\n                )\n\n            if (options.uniques)\n                this.uniques = options.uniques.map(\n                    (unique) => new TableUnique(unique),\n                )\n\n            if (options.checks)\n                this.checks = options.checks.map(\n                    (check) => new TableCheck(check),\n                )\n\n            if (options.exclusions)\n                this.exclusions = options.exclusions.map(\n                    (exclusion) => new TableExclusion(exclusion),\n                )\n\n            if (options.justCreated !== undefined)\n                this.justCreated = options.justCreated\n\n            if (options.withoutRowid) this.withoutRowid = options.withoutRowid\n\n            this.engine = options.engine\n\n            this.comment = options.comment\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Accessors\n    // -------------------------------------------------------------------------\n\n    get primaryColumns(): TableColumn[] {\n        return this.columns.filter((column) => column.isPrimary)\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Clones this table to a new table with all properties cloned.\n     */\n    clone(): Table {\n        return new Table({\n            schema: this.schema,\n            database: this.database,\n            name: this.name,\n            columns: this.columns.map((column) => column.clone()),\n            indices: this.indices.map((constraint) => constraint.clone()),\n            foreignKeys: this.foreignKeys.map((constraint) =>\n                constraint.clone(),\n            ),\n            uniques: this.uniques.map((constraint) => constraint.clone()),\n            checks: this.checks.map((constraint) => constraint.clone()),\n            exclusions: this.exclusions.map((constraint) => constraint.clone()),\n            justCreated: this.justCreated,\n            withoutRowid: this.withoutRowid,\n            engine: this.engine,\n            comment: this.comment,\n        })\n    }\n\n    /**\n     * Add column and creates its constraints.\n     */\n    addColumn(column: TableColumn): void {\n        this.columns.push(column)\n    }\n\n    /**\n     * Remove column and its constraints.\n     */\n    removeColumn(column: TableColumn): void {\n        const foundColumn = this.columns.find((c) => c.name === column.name)\n        if (foundColumn)\n            this.columns.splice(this.columns.indexOf(foundColumn), 1)\n    }\n\n    /**\n     * Adds unique constraint.\n     */\n    addUniqueConstraint(uniqueConstraint: TableUnique): void {\n        this.uniques.push(uniqueConstraint)\n        if (uniqueConstraint.columnNames.length === 1) {\n            const uniqueColumn = this.columns.find(\n                (column) => column.name === uniqueConstraint.columnNames[0],\n            )\n            if (uniqueColumn) uniqueColumn.isUnique = true\n        }\n    }\n\n    /**\n     * Removes unique constraint.\n     */\n    removeUniqueConstraint(removedUnique: TableUnique): void {\n        const foundUnique = this.uniques.find(\n            (unique) => unique.name === removedUnique.name,\n        )\n        if (foundUnique) {\n            this.uniques.splice(this.uniques.indexOf(foundUnique), 1)\n            if (foundUnique.columnNames.length === 1) {\n                const uniqueColumn = this.columns.find(\n                    (column) => column.name === foundUnique.columnNames[0],\n                )\n                if (uniqueColumn) uniqueColumn.isUnique = false\n            }\n        }\n    }\n\n    /**\n     * Adds check constraint.\n     */\n    addCheckConstraint(checkConstraint: TableCheck): void {\n        this.checks.push(checkConstraint)\n    }\n\n    /**\n     * Removes check constraint.\n     */\n    removeCheckConstraint(removedCheck: TableCheck): void {\n        const foundCheck = this.checks.find(\n            (check) => check.name === removedCheck.name,\n        )\n        if (foundCheck) {\n            this.checks.splice(this.checks.indexOf(foundCheck), 1)\n        }\n    }\n\n    /**\n     * Adds exclusion constraint.\n     */\n    addExclusionConstraint(exclusionConstraint: TableExclusion): void {\n        this.exclusions.push(exclusionConstraint)\n    }\n\n    /**\n     * Removes exclusion constraint.\n     */\n    removeExclusionConstraint(removedExclusion: TableExclusion): void {\n        const foundExclusion = this.exclusions.find(\n            (exclusion) => exclusion.name === removedExclusion.name,\n        )\n        if (foundExclusion) {\n            this.exclusions.splice(this.exclusions.indexOf(foundExclusion), 1)\n        }\n    }\n\n    /**\n     * Adds foreign keys.\n     */\n    addForeignKey(foreignKey: TableForeignKey): void {\n        this.foreignKeys.push(foreignKey)\n    }\n\n    /**\n     * Removes foreign key.\n     */\n    removeForeignKey(removedForeignKey: TableForeignKey): void {\n        const fk = this.foreignKeys.find(\n            (foreignKey) => foreignKey.name === removedForeignKey.name,\n        )\n        if (fk) this.foreignKeys.splice(this.foreignKeys.indexOf(fk), 1)\n    }\n\n    /**\n     * Adds index.\n     */\n    addIndex(index: TableIndex, isMysql: boolean = false): void {\n        this.indices.push(index)\n\n        // in Mysql unique indices and unique constraints are the same thing\n        // if index is unique and have only one column, we mark this column as unique\n        if (index.columnNames.length === 1 && index.isUnique && isMysql) {\n            const column = this.columns.find(\n                (c) => c.name === index.columnNames[0],\n            )\n            if (column) column.isUnique = true\n        }\n    }\n\n    /**\n     * Removes index.\n     */\n    removeIndex(tableIndex: TableIndex, isMysql: boolean = false): void {\n        const index = this.indices.find(\n            (index) => index.name === tableIndex.name,\n        )\n        if (index) {\n            this.indices.splice(this.indices.indexOf(index), 1)\n\n            // in Mysql unique indices and unique constraints are the same thing\n            // if index is unique and have only one column, we move `unique` attribute from its column\n            if (index.columnNames.length === 1 && index.isUnique && isMysql) {\n                const column = this.columns.find(\n                    (c) => c.name === index.columnNames[0],\n                )\n                if (column)\n                    column.isUnique = this.indices.some(\n                        (ind) =>\n                            ind.columnNames.length === 1 &&\n                            ind.columnNames[0] === column.name &&\n                            !!index.isUnique,\n                    )\n            }\n        }\n    }\n\n    findColumnByName(name: string): TableColumn | undefined {\n        return this.columns.find((column) => column.name === name)\n    }\n\n    /**\n     * Returns all column indices.\n     */\n    findColumnIndices(column: TableColumn): TableIndex[] {\n        return this.indices.filter((index) => {\n            return !!index.columnNames.find(\n                (columnName) => columnName === column.name,\n            )\n        })\n    }\n\n    /**\n     * Returns all column foreign keys.\n     */\n    findColumnForeignKeys(column: TableColumn): TableForeignKey[] {\n        return this.foreignKeys.filter((foreignKey) => {\n            return !!foreignKey.columnNames.find(\n                (columnName) => columnName === column.name,\n            )\n        })\n    }\n\n    /**\n     * Returns all column uniques.\n     */\n    findColumnUniques(column: TableColumn): TableUnique[] {\n        return this.uniques.filter((unique) => {\n            return !!unique.columnNames.find(\n                (columnName) => columnName === column.name,\n            )\n        })\n    }\n\n    /**\n     * Returns all column checks.\n     */\n    findColumnChecks(column: TableColumn): TableCheck[] {\n        return this.checks.filter((check) => {\n            return !!check.columnNames!.find(\n                (columnName) => columnName === column.name,\n            )\n        })\n    }\n\n    // -------------------------------------------------------------------------\n    // Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates table from a given entity metadata.\n     */\n    static create(entityMetadata: EntityMetadata, driver: Driver): Table {\n        const database =\n            entityMetadata.database === driver.database\n                ? undefined\n                : entityMetadata.database\n        const schema =\n            entityMetadata.schema === (driver.options as any).schema\n                ? undefined\n                : entityMetadata.schema\n\n        const options: TableOptions = {\n            database: entityMetadata.database,\n            schema: entityMetadata.schema,\n            name: driver.buildTableName(\n                entityMetadata.tableName,\n                schema,\n                database,\n            ),\n            withoutRowid: entityMetadata.withoutRowid,\n            engine: entityMetadata.engine,\n            columns: entityMetadata.columns\n                .filter((column) => column && !column.isVirtualProperty)\n                .map((column) =>\n                    TableUtils.createTableColumnOptions(column, driver),\n                ),\n            indices: entityMetadata.indices\n                .filter((index) => index.synchronize === true)\n                .map((index) => TableIndex.create(index)),\n            uniques: entityMetadata.uniques.map((unique) =>\n                TableUnique.create(unique),\n            ),\n            checks: entityMetadata.checks.map((check) =>\n                TableCheck.create(check),\n            ),\n            exclusions: entityMetadata.exclusions.map((exclusion) =>\n                TableExclusion.create(exclusion),\n            ),\n            comment: entityMetadata.comment,\n        }\n\n        return new Table(options)\n    }\n}\n"], "sourceRoot": "../.."}