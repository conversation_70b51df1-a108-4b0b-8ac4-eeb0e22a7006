{"version": 3, "sources": ["../../src/metadata/ExclusionMetadata.ts"], "names": [], "mappings": ";;;AAIA;;GAEG;AACH,MAAa,iBAAiB;IAgC1B,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAY,OAGX;QACG,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAE5C,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;YACjC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,CAAA;YACzC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAA;QACtC,CAAC;IACL,CAAC;IAED,wEAAwE;IACxE,uBAAuB;IACvB,wEAAwE;IAExE;;;OAGG;IACH,KAAK,CAAC,cAAuC;QACzC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS;YACtB,CAAC,CAAC,IAAI,CAAC,SAAS;YAChB,CAAC,CAAC,cAAc,CAAC,uBAAuB,CAClC,IAAI,CAAC,cAAc,CAAC,SAAS,EAC7B,IAAI,CAAC,UAAU,CAClB,CAAA;QACP,OAAO,IAAI,CAAA;IACf,CAAC;CACJ;AAlED,8CAkEC", "file": "ExclusionMetadata.js", "sourcesContent": ["import { EntityMetadata } from \"./EntityMetadata\"\nimport { NamingStrategyInterface } from \"../naming-strategy/NamingStrategyInterface\"\nimport { ExclusionMetadataArgs } from \"../metadata-args/ExclusionMetadataArgs\"\n\n/**\n * Exclusion metadata contains all information about table's exclusion constraints.\n */\nexport class ExclusionMetadata {\n    // ---------------------------------------------------------------------\n    // Public Properties\n    // ---------------------------------------------------------------------\n\n    /**\n     * Entity metadata of the class to which this exclusion constraint is applied.\n     */\n    entityMetadata: EntityMetadata\n\n    /**\n     * Target class to which metadata is applied.\n     */\n    target?: Function | string\n\n    /**\n     * Exclusion expression.\n     */\n    expression: string\n\n    /**\n     * User specified exclusion constraint name.\n     */\n    givenName?: string\n\n    /**\n     * Final exclusion constraint name.\n     * If exclusion constraint name was given by a user then it stores normalized (by naming strategy) givenName.\n     * If exclusion constraint name was not given then its generated.\n     */\n    name: string\n\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(options: {\n        entityMetadata: EntityMetadata\n        args?: ExclusionMetadataArgs\n    }) {\n        this.entityMetadata = options.entityMetadata\n\n        if (options.args) {\n            this.target = options.args.target\n            this.expression = options.args.expression\n            this.givenName = options.args.name\n        }\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Build Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Builds some depend exclusion constraint properties.\n     * Must be called after all entity metadata's properties map, columns and relations are built.\n     */\n    build(namingStrategy: NamingStrategyInterface): this {\n        this.name = this.givenName\n            ? this.givenName\n            : namingStrategy.exclusionConstraintName(\n                  this.entityMetadata.tableName,\n                  this.expression,\n              )\n        return this\n    }\n}\n"], "sourceRoot": ".."}