{"version": 3, "sources": ["../../src/entity-schema/EntitySchemaTransformer.ts"], "names": [], "mappings": ";;;AACA,8EAA0E;AAgB1E,2EAAuE;AAKvE;;;GAGG;AACH,MAAa,uBAAuB;IAChC,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,SAAS,CAAC,OAA4B;QAClC,MAAM,mBAAmB,GAAG,IAAI,yCAAmB,EAAE,CAAA;QAErD,OAAO,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YAC7B,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAA;YAEpC,0CAA0C;YAC1C,MAAM,aAAa,GAAsB;gBACrC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;gBACtC,IAAI,EAAE,OAAO,CAAC,SAAS;gBACvB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,SAAS;gBAC/B,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY;gBACpC,UAAU,EAAE,OAAO,CAAC,UAAU;aACjC,CAAA;YACD,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YAE9C,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,CAAA;YAE/B,IAAI,WAAW,EAAE,CAAC;gBACd,mBAAmB,CAAC,YAAY,CAAC,IAAI,CAAC;oBAClC,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,OAAO,EAAE,WAAW,CAAC,OAAO,IAAI,KAAK;oBACrC,MAAM,EAAE,WAAW,CAAC,MAAM;wBACtB,CAAC,CAAC,OAAO,WAAW,CAAC,MAAM,KAAK,QAAQ;4BACpC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,MAAM,EAAE;4BAC9B,CAAC,CAAC,WAAW,CAAC,MAAM;wBACxB,CAAC,CAAC,SAAS;iBACS,CAAC,CAAA;YACjC,CAAC;YAED,MAAM,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAA;YAEtC,IAAI,kBAAkB,EAAE,CAAC;gBACrB,mBAAmB,CAAC,mBAAmB,CAAC,IAAI,CAAC;oBACzC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,KAAK,EAAE,kBAAkB;iBAC5B,CAAC,CAAA;YACN,CAAC;YAED,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAA;QAChE,CAAC,CAAC,CAAA;QAEF,OAAO,mBAAmB,CAAA;IAC9B,CAAC;IAEO,yBAAyB,CAC7B,OAAiC,EACjC,mBAAwC;QAExC,4CAA4C;QAC5C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YAChD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAE,CAAA;YAE3C,MAAM,aAAa,GAAG,MAAmC,CAAA;YACzD,IAAI,IAAI,GAAe,SAAS,CAAA;YAChC,IAAI,aAAa,CAAC,UAAU;gBAAE,IAAI,GAAG,YAAY,CAAA;YACjD,IAAI,aAAa,CAAC,UAAU;gBAAE,IAAI,GAAG,YAAY,CAAA;YACjD,IAAI,aAAa,CAAC,UAAU;gBAAE,IAAI,GAAG,YAAY,CAAA;YACjD,IAAI,aAAa,CAAC,OAAO;gBAAE,IAAI,GAAG,SAAS,CAAA;YAC3C,IAAI,aAAa,CAAC,iBAAiB;gBAAE,IAAI,GAAG,mBAAmB,CAAA;YAC/D,IAAI,aAAa,CAAC,SAAS;gBAAE,IAAI,GAAG,WAAW,CAAA;YAC/C,IAAI,aAAa,CAAC,QAAQ;gBAAE,IAAI,GAAG,UAAU,CAAA;YAE7C,MAAM,UAAU,GAAuB;gBACnC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;gBACtC,IAAI,EAAE,IAAI;gBACV,YAAY,EAAE,UAAU;gBACxB,OAAO,EAAE;oBACL,IAAI,EAAE,aAAa,CAAC,IAAI;oBACxB,IAAI,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI;oBACzD,wBAAwB,EACpB,aAAa,CAAC,wBAAwB;oBAC1C,MAAM,EAAE,aAAa,CAAC,MAAM;oBAC5B,KAAK,EAAE,aAAa,CAAC,KAAK;oBAC1B,QAAQ,EAAE,aAAa,CAAC,QAAQ;oBAChC,QAAQ,EAAE,aAAa,CAAC,QAAQ;oBAChC,MAAM,EAAE,aAAa,CAAC,MAAM;oBAC5B,MAAM,EAAE,aAAa,CAAC,MAAM;oBAC5B,MAAM,EAAE,aAAa,CAAC,MAAM;oBAC5B,OAAO,EAAE,aAAa,CAAC,OAAO;oBAC9B,MAAM,EAAE,aAAa,CAAC,MAAM;oBAC5B,OAAO,EAAE,aAAa,CAAC,OAAO;oBAC9B,OAAO,EAAE,aAAa,CAAC,OAAO;oBAC9B,QAAQ,EAAE,aAAa,CAAC,QAAQ;oBAChC,SAAS,EAAE,aAAa,CAAC,SAAS;oBAClC,KAAK,EAAE,aAAa,CAAC,KAAK;oBAC1B,QAAQ,EAAE,aAAa,CAAC,QAAQ;oBAChC,QAAQ,EAAE,aAAa,CAAC,QAAQ;oBAChC,OAAO,EAAE,aAAa,CAAC,OAAO;oBAC9B,SAAS,EAAE,aAAa,CAAC,SAAS;oBAClC,IAAI,EAAE,aAAa,CAAC,IAAI;oBACxB,QAAQ,EAAE,aAAa,CAAC,QAAQ;oBAChC,YAAY,EAAE,aAAa,CAAC,YAAY;oBACxC,aAAa,EAAE,aAAa,CAAC,aAAa;oBAC1C,UAAU,EAAE,aAAa,CAAC,UAAU;oBACpC,KAAK,EAAE,aAAa,CAAC,KAAK;oBAC1B,WAAW,EAAE,aAAa,CAAC,WAAW;oBACtC,kBAAkB,EAAE,aAAa,CAAC,kBAAkB;oBACpD,IAAI,EAAE,aAAa,CAAC,IAAI;iBAC3B;aACJ,CAAA;YACD,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAE5C,IAAI,aAAa,CAAC,SAAS,EAAE,CAAC;gBAC1B,MAAM,cAAc,GAA0B;oBAC1C,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,YAAY,EAAE,UAAU;oBACxB,QAAQ,EACJ,OAAO,aAAa,CAAC,SAAS,KAAK,QAAQ;wBACvC,CAAC,CAAC,aAAa,CAAC,SAAS;wBACzB,CAAC,CAAC,WAAW;iBACxB,CAAA;gBACD,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YACxD,CAAC;YAED,IAAI,aAAa,CAAC,MAAM;gBACpB,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC;oBAC7B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,OAAO,EAAE,CAAC,UAAU,CAAC;iBACxB,CAAC,CAAA;YAEN,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC;gBAC3B,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAA;gBAE3C,MAAM,cAAc,GAA2B;oBAC3C,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,IAAI,EAAE,UAAU,CAAC,MAAM;oBACvB,YAAY,EAAE,UAAU;oBACxB,WAAW,EAAE,UAAU,CAAC,WAAW;oBACnC,IAAI,EAAE,UAAU,CAAC,IAAI;oBACrB,QAAQ,EAAE,UAAU,CAAC,QAAQ;oBAC7B,QAAQ,EAAE,UAAU,CAAC,QAAQ;oBAC7B,UAAU,EAAE,UAAU,CAAC,UAAU;iBACpC,CAAA;gBACD,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YACxD,CAAC;QACL,CAAC,CAAC,CAAA;QAEF,6CAA6C;QAC7C,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;gBACpD,MAAM,cAAc,GAAG,OAAO,CAAC,SAAU,CAAC,YAAY,CAAE,CAAA;gBACxD,MAAM,QAAQ,GAAyB;oBACnC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,YAAY,EAAE,YAAY;oBAC1B,YAAY,EAAE,cAAc,CAAC,IAAI;oBACjC,MAAM,EAAE,cAAc,CAAC,IAAI,IAAI,KAAK;oBACpC,IAAI,EAAE,cAAc,CAAC,MAAM;oBAC3B,mBAAmB,EAAE,cAAc,CAAC,WAAW;oBAC/C,YAAY,EAAE,cAAc,CAAC,UAAU;oBACvC,cAAc,EAAE,cAAc,CAAC,YAAY;oBAC3C,OAAO,EAAE;wBACL,KAAK,EAAE,cAAc,CAAC,KAAK,IAAI,KAAK;wBACpC,OAAO,EAAE,cAAc,CAAC,OAAO;wBAC/B,QAAQ,EAAE,cAAc,CAAC,QAAQ;wBACjC,QAAQ,EAAE,cAAc,CAAC,QAAQ;wBACjC,QAAQ,EAAE,cAAc,CAAC,QAAQ;wBACjC,UAAU,EAAE,cAAc,CAAC,UAAU;wBACrC,mCAAmC;wBACnC,2BAA2B,EACvB,cAAc,CAAC,2BAA2B;wBAC9C,WAAW,EAAE,cAAc,CAAC,WAAW;wBACvC,iBAAiB,EAAE,cAAc,CAAC,iBAAiB;qBACtD;iBACJ,CAAA;gBAED,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAE5C,kBAAkB;gBAClB,IAAI,cAAc,CAAC,UAAU,EAAE,CAAC;oBAC5B,IAAI,OAAO,cAAc,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;wBACjD,MAAM,UAAU,GAA2B;4BACvC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;4BACtC,YAAY,EAAE,YAAY;yBAC7B,CAAA;wBACD,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;oBACpD,CAAC;yBAAM,CAAC;wBACJ,MAAM,kBAAkB,GAAG,KAAK,CAAC,OAAO,CACpC,cAAc,CAAC,UAAU,CAC5B;4BACG,CAAC,CAAC,cAAc,CAAC,UAAU;4BAC3B,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAA;wBAEjC,KAAK,MAAM,gBAAgB,IAAI,kBAAkB,EAAE,CAAC;4BAChD,MAAM,UAAU,GAA2B;gCACvC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;gCACtC,YAAY,EAAE,YAAY;gCAC1B,IAAI,EAAE,gBAAgB,CAAC,IAAI;gCAC3B,oBAAoB,EAChB,gBAAgB,CAAC,oBAAoB;gCACzC,wBAAwB,EACpB,gBAAgB,CAAC,wBAAwB;6BAChD,CAAA;4BACD,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;wBACpD,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,iBAAiB;gBACjB,IAAI,cAAc,CAAC,SAAS,EAAE,CAAC;oBAC3B,IAAI,OAAO,cAAc,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;wBAChD,MAAM,SAAS,GAA0B;4BACrC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;4BACtC,YAAY,EAAE,YAAY;yBAC7B,CAAA;wBACD,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBAClD,CAAC;yBAAM,CAAC;wBACJ,MAAM,SAAS,GAA0B;4BACrC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;4BACtC,YAAY,EAAE,YAAY;4BAC1B,IAAI,EAAE,cAAc,CAAC,SAAS,CAAC,IAAI;4BACnC,QAAQ,EAAE,cAAc,CAAC,SAAS,CAAC,QAAQ;4BAC3C,MAAM,EAAE,cAAc,CAAC,SAAS,CAAC,MAAM;4BACvC,WAAW,EAAE,CACT,cAAc,CAAC,SAClB,CAAC,UAAU;gCACR,CAAC,CAAC;oCAEQ,cAAc,CAAC,SAClB,CAAC,UAAW;iCAChB;gCACH,CAAC,CACK,cAAc,CAAC,SAClB,CAAC,WAAW,CAAQ;4BAC3B,kBAAkB,EAAE,CAChB,cAAc,CAAC,SAClB,CAAC,iBAAiB;gCACf,CAAC,CAAC;oCAEQ,cAAc,CAAC,SAClB,CAAC,iBAAkB;iCACvB;gCACH,CAAC,CACK,cAAc,CAAC,SAClB,CAAC,kBAAkB,CAAQ;yBACrC,CAAA;wBACD,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBAClD,CAAC;gBACL,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,gDAAgD;QAChD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;gBACxD,MAAM,iBAAiB,GAAG,OAAO,CAAC,WAAY,CAAC,cAAc,CAAE,CAAA;gBAC/D,MAAM,UAAU,GAA2B;oBACvC,YAAY,EAAE,cAAc;oBAC5B,QAAQ,EAAE,iBAAiB,CAAC,YAAY;oBACxC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,KAAK,EAAE,iBAAiB,CAAC,KAAK;oBAC9B,mBAAmB,EAAE,iBAAiB,CAAC,mBAAmB;iBAC7D,CAAA;gBACD,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YACpD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,0CAA0C;QAC1C,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC9B,MAAM,SAAS,GAAsB;oBACjC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,MAAM,EAAE,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;oBAC5C,OAAO,EAAE,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;oBAC9C,QAAQ,EAAE,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;oBAChD,YAAY,EAAE,KAAK,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;oBACxD,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,WAAW,EAAE,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;oBACvD,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,OAAO,EAAE,KAAK,CAAC,OAAO;iBACzB,CAAA;gBACD,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC/C,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACvC,MAAM,cAAc,GAA2B;oBAC3C,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,IAAI,EAAE,UAAU,CAAC,MAAM;oBACvB,WAAW,EAAE,UAAU,CAAC,WAAW;oBACnC,qBAAqB,EAAE,UAAU,CAAC,qBAAqB;oBACvD,IAAI,EAAE,UAAU,CAAC,IAAI;oBACrB,QAAQ,EAAE,UAAU,CAAC,QAAQ;oBAC7B,QAAQ,EAAE,UAAU,CAAC,QAAQ;oBAC7B,UAAU,EAAE,UAAU,CAAC,UAAU;iBACpC,CAAA;gBACD,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YACxD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,2CAA2C;QAC3C,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC/B,MAAM,UAAU,GAAuB;oBACnC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,UAAU,EAAE,MAAM,CAAC,UAAU;iBAChC,CAAA;gBACD,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAChD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,0CAA0C;QAC1C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC7B,MAAM,SAAS,GAAsB;oBACjC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,UAAU,EAAE,KAAK,CAAC,UAAU;iBAC/B,CAAA;gBACD,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC9C,CAAC,CAAC,CAAA;QACN,CAAC;QAED,8CAA8C;QAC9C,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;gBACrC,MAAM,aAAa,GAA0B;oBACzC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,UAAU,EAAE,SAAS,CAAC,UAAU;iBACnC,CAAA;gBACD,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YACtD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAClD,MAAM,eAAe,GAAG,OAAO,CAAC,SAAU,CAAC,UAAU,CAAC,CAAA;gBAEtD,IAAI,CAAC,eAAe,CAAC,MAAM;oBACvB,MAAM,qDAAyB,CAAC,qCAAqC,CACjE,UAAU,CACb,CAAA;gBAEL,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,OAAO,CAAA;gBAErD,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC;oBAC/B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI;oBACtC,YAAY,EAAE,UAAU;oBACxB,OAAO,EAAE,eAAe,CAAC,KAAK,KAAK,IAAI;oBACvC,MAAM,EACF,eAAe,CAAC,MAAM,KAAK,SAAS;wBAChC,CAAC,CAAC,eAAe,CAAC,MAAM;wBACxB,CAAC,CAAC,SAAS;oBACnB,IAAI,EAAE,GAAG,EAAE,CAAC,cAAc,EAAE,MAAM,IAAI,cAAc,CAAC,IAAI;iBAC5D,CAAC,CAAA;gBAEF,IAAI,CAAC,yBAAyB,CAC1B,cAAc,EACd,mBAAmB,CACtB,CAAA;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;CACJ;AAnXD,0DAmXC", "file": "EntitySchemaTransformer.js", "sourcesContent": ["import { EntitySchema } from \"./EntitySchema\"\nimport { MetadataArgsStorage } from \"../metadata-args/MetadataArgsStorage\"\nimport { TableMetadataArgs } from \"../metadata-args/TableMetadataArgs\"\nimport { ColumnMetadataArgs } from \"../metadata-args/ColumnMetadataArgs\"\nimport { IndexMetadataArgs } from \"../metadata-args/IndexMetadataArgs\"\nimport { RelationMetadataArgs } from \"../metadata-args/RelationMetadataArgs\"\nimport { JoinColumnMetadataArgs } from \"../metadata-args/JoinColumnMetadataArgs\"\nimport { JoinTableMetadataArgs } from \"../metadata-args/JoinTableMetadataArgs\"\nimport { JoinTableOptions } from \"../decorator/options/JoinTableOptions\"\nimport { JoinTableMultipleColumnsOptions } from \"../decorator/options/JoinTableMultipleColumnsOptions\"\nimport { ColumnMode } from \"../metadata-args/types/ColumnMode\"\nimport { GeneratedMetadataArgs } from \"../metadata-args/GeneratedMetadataArgs\"\nimport { UniqueMetadataArgs } from \"../metadata-args/UniqueMetadataArgs\"\nimport { CheckMetadataArgs } from \"../metadata-args/CheckMetadataArgs\"\nimport { ExclusionMetadataArgs } from \"../metadata-args/ExclusionMetadataArgs\"\nimport { EntitySchemaColumnOptions } from \"./EntitySchemaColumnOptions\"\nimport { EntitySchemaOptions } from \"./EntitySchemaOptions\"\nimport { EntitySchemaEmbeddedError } from \"./EntitySchemaEmbeddedError\"\nimport { InheritanceMetadataArgs } from \"../metadata-args/InheritanceMetadataArgs\"\nimport { RelationIdMetadataArgs } from \"../metadata-args/RelationIdMetadataArgs\"\nimport { ForeignKeyMetadataArgs } from \"../metadata-args/ForeignKeyMetadataArgs\"\n\n/**\n * Transforms entity schema into metadata args storage.\n * The result will be just like entities read from decorators.\n */\nexport class EntitySchemaTransformer {\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Transforms entity schema into new metadata args storage object.\n     */\n    transform(schemas: EntitySchema<any>[]): MetadataArgsStorage {\n        const metadataArgsStorage = new MetadataArgsStorage()\n\n        schemas.forEach((entitySchema) => {\n            const options = entitySchema.options\n\n            // add table metadata args from the schema\n            const tableMetadata: TableMetadataArgs = {\n                target: options.target || options.name,\n                name: options.tableName,\n                database: options.database,\n                schema: options.schema,\n                type: options.type || \"regular\",\n                orderBy: options.orderBy,\n                synchronize: options.synchronize,\n                withoutRowid: !!options.withoutRowid,\n                expression: options.expression,\n            }\n            metadataArgsStorage.tables.push(tableMetadata)\n\n            const { inheritance } = options\n\n            if (inheritance) {\n                metadataArgsStorage.inheritances.push({\n                    target: options.target,\n                    pattern: inheritance.pattern ?? \"STI\",\n                    column: inheritance.column\n                        ? typeof inheritance.column === \"string\"\n                            ? { name: inheritance.column }\n                            : inheritance.column\n                        : undefined,\n                } as InheritanceMetadataArgs)\n            }\n\n            const { discriminatorValue } = options\n\n            if (discriminatorValue) {\n                metadataArgsStorage.discriminatorValues.push({\n                    target: options.target || options.name,\n                    value: discriminatorValue,\n                })\n            }\n\n            this.transformColumnsRecursive(options, metadataArgsStorage)\n        })\n\n        return metadataArgsStorage\n    }\n\n    private transformColumnsRecursive(\n        options: EntitySchemaOptions<any>,\n        metadataArgsStorage: MetadataArgsStorage,\n    ): void {\n        // add columns metadata args from the schema\n        Object.keys(options.columns).forEach((columnName) => {\n            const column = options.columns[columnName]!\n\n            const regularColumn = column as EntitySchemaColumnOptions\n            let mode: ColumnMode = \"regular\"\n            if (regularColumn.createDate) mode = \"createDate\"\n            if (regularColumn.updateDate) mode = \"updateDate\"\n            if (regularColumn.deleteDate) mode = \"deleteDate\"\n            if (regularColumn.version) mode = \"version\"\n            if (regularColumn.treeChildrenCount) mode = \"treeChildrenCount\"\n            if (regularColumn.treeLevel) mode = \"treeLevel\"\n            if (regularColumn.objectId) mode = \"objectId\"\n\n            const columnArgs: ColumnMetadataArgs = {\n                target: options.target || options.name,\n                mode: mode,\n                propertyName: columnName,\n                options: {\n                    type: regularColumn.type,\n                    name: regularColumn.objectId ? \"_id\" : regularColumn.name,\n                    primaryKeyConstraintName:\n                        regularColumn.primaryKeyConstraintName,\n                    length: regularColumn.length,\n                    width: regularColumn.width,\n                    nullable: regularColumn.nullable,\n                    readonly: regularColumn.readonly,\n                    update: regularColumn.update,\n                    select: regularColumn.select,\n                    insert: regularColumn.insert,\n                    primary: regularColumn.primary,\n                    unique: regularColumn.unique,\n                    comment: regularColumn.comment,\n                    default: regularColumn.default,\n                    onUpdate: regularColumn.onUpdate,\n                    precision: regularColumn.precision,\n                    scale: regularColumn.scale,\n                    zerofill: regularColumn.zerofill,\n                    unsigned: regularColumn.unsigned,\n                    charset: regularColumn.charset,\n                    collation: regularColumn.collation,\n                    enum: regularColumn.enum,\n                    enumName: regularColumn.enumName,\n                    asExpression: regularColumn.asExpression,\n                    generatedType: regularColumn.generatedType,\n                    hstoreType: regularColumn.hstoreType,\n                    array: regularColumn.array,\n                    transformer: regularColumn.transformer,\n                    spatialFeatureType: regularColumn.spatialFeatureType,\n                    srid: regularColumn.srid,\n                },\n            }\n            metadataArgsStorage.columns.push(columnArgs)\n\n            if (regularColumn.generated) {\n                const generationArgs: GeneratedMetadataArgs = {\n                    target: options.target || options.name,\n                    propertyName: columnName,\n                    strategy:\n                        typeof regularColumn.generated === \"string\"\n                            ? regularColumn.generated\n                            : \"increment\",\n                }\n                metadataArgsStorage.generations.push(generationArgs)\n            }\n\n            if (regularColumn.unique)\n                metadataArgsStorage.uniques.push({\n                    target: options.target || options.name,\n                    columns: [columnName],\n                })\n\n            if (regularColumn.foreignKey) {\n                const foreignKey = regularColumn.foreignKey\n\n                const foreignKeyArgs: ForeignKeyMetadataArgs = {\n                    target: options.target || options.name,\n                    type: foreignKey.target,\n                    propertyName: columnName,\n                    inverseSide: foreignKey.inverseSide,\n                    name: foreignKey.name,\n                    onDelete: foreignKey.onDelete,\n                    onUpdate: foreignKey.onUpdate,\n                    deferrable: foreignKey.deferrable,\n                }\n                metadataArgsStorage.foreignKeys.push(foreignKeyArgs)\n            }\n        })\n\n        // add relation metadata args from the schema\n        if (options.relations) {\n            Object.keys(options.relations).forEach((relationName) => {\n                const relationSchema = options.relations![relationName]!\n                const relation: RelationMetadataArgs = {\n                    target: options.target || options.name,\n                    propertyName: relationName,\n                    relationType: relationSchema.type,\n                    isLazy: relationSchema.lazy || false,\n                    type: relationSchema.target,\n                    inverseSideProperty: relationSchema.inverseSide,\n                    isTreeParent: relationSchema.treeParent,\n                    isTreeChildren: relationSchema.treeChildren,\n                    options: {\n                        eager: relationSchema.eager || false,\n                        cascade: relationSchema.cascade,\n                        nullable: relationSchema.nullable,\n                        onDelete: relationSchema.onDelete,\n                        onUpdate: relationSchema.onUpdate,\n                        deferrable: relationSchema.deferrable,\n                        // primary: relationSchema.primary,\n                        createForeignKeyConstraints:\n                            relationSchema.createForeignKeyConstraints,\n                        persistence: relationSchema.persistence,\n                        orphanedRowAction: relationSchema.orphanedRowAction,\n                    },\n                }\n\n                metadataArgsStorage.relations.push(relation)\n\n                // add join column\n                if (relationSchema.joinColumn) {\n                    if (typeof relationSchema.joinColumn === \"boolean\") {\n                        const joinColumn: JoinColumnMetadataArgs = {\n                            target: options.target || options.name,\n                            propertyName: relationName,\n                        }\n                        metadataArgsStorage.joinColumns.push(joinColumn)\n                    } else {\n                        const joinColumnsOptions = Array.isArray(\n                            relationSchema.joinColumn,\n                        )\n                            ? relationSchema.joinColumn\n                            : [relationSchema.joinColumn]\n\n                        for (const joinColumnOption of joinColumnsOptions) {\n                            const joinColumn: JoinColumnMetadataArgs = {\n                                target: options.target || options.name,\n                                propertyName: relationName,\n                                name: joinColumnOption.name,\n                                referencedColumnName:\n                                    joinColumnOption.referencedColumnName,\n                                foreignKeyConstraintName:\n                                    joinColumnOption.foreignKeyConstraintName,\n                            }\n                            metadataArgsStorage.joinColumns.push(joinColumn)\n                        }\n                    }\n                }\n\n                // add join table\n                if (relationSchema.joinTable) {\n                    if (typeof relationSchema.joinTable === \"boolean\") {\n                        const joinTable: JoinTableMetadataArgs = {\n                            target: options.target || options.name,\n                            propertyName: relationName,\n                        }\n                        metadataArgsStorage.joinTables.push(joinTable)\n                    } else {\n                        const joinTable: JoinTableMetadataArgs = {\n                            target: options.target || options.name,\n                            propertyName: relationName,\n                            name: relationSchema.joinTable.name,\n                            database: relationSchema.joinTable.database,\n                            schema: relationSchema.joinTable.schema,\n                            joinColumns: ((\n                                relationSchema.joinTable as JoinTableOptions\n                            ).joinColumn\n                                ? [\n                                      (\n                                          relationSchema.joinTable as JoinTableOptions\n                                      ).joinColumn!,\n                                  ]\n                                : (\n                                      relationSchema.joinTable as JoinTableMultipleColumnsOptions\n                                  ).joinColumns) as any,\n                            inverseJoinColumns: ((\n                                relationSchema.joinTable as JoinTableOptions\n                            ).inverseJoinColumn\n                                ? [\n                                      (\n                                          relationSchema.joinTable as JoinTableOptions\n                                      ).inverseJoinColumn!,\n                                  ]\n                                : (\n                                      relationSchema.joinTable as JoinTableMultipleColumnsOptions\n                                  ).inverseJoinColumns) as any,\n                        }\n                        metadataArgsStorage.joinTables.push(joinTable)\n                    }\n                }\n            })\n        }\n\n        // add relation id metadata args from the schema\n        if (options.relationIds) {\n            Object.keys(options.relationIds).forEach((relationIdName) => {\n                const relationIdOptions = options.relationIds![relationIdName]!\n                const relationId: RelationIdMetadataArgs = {\n                    propertyName: relationIdName,\n                    relation: relationIdOptions.relationName,\n                    target: options.target || options.name,\n                    alias: relationIdOptions.alias,\n                    queryBuilderFactory: relationIdOptions.queryBuilderFactory,\n                }\n                metadataArgsStorage.relationIds.push(relationId)\n            })\n        }\n\n        // add index metadata args from the schema\n        if (options.indices) {\n            options.indices.forEach((index) => {\n                const indexArgs: IndexMetadataArgs = {\n                    target: options.target || options.name,\n                    name: index.name,\n                    unique: index.unique === true ? true : false,\n                    spatial: index.spatial === true ? true : false,\n                    fulltext: index.fulltext === true ? true : false,\n                    nullFiltered: index.nullFiltered === true ? true : false,\n                    parser: index.parser,\n                    synchronize: index.synchronize === false ? false : true,\n                    where: index.where,\n                    sparse: index.sparse,\n                    columns: index.columns,\n                }\n                metadataArgsStorage.indices.push(indexArgs)\n            })\n        }\n\n        if (options.foreignKeys) {\n            options.foreignKeys.forEach((foreignKey) => {\n                const foreignKeyArgs: ForeignKeyMetadataArgs = {\n                    target: options.target || options.name,\n                    type: foreignKey.target,\n                    columnNames: foreignKey.columnNames,\n                    referencedColumnNames: foreignKey.referencedColumnNames,\n                    name: foreignKey.name,\n                    onDelete: foreignKey.onDelete,\n                    onUpdate: foreignKey.onUpdate,\n                    deferrable: foreignKey.deferrable,\n                }\n                metadataArgsStorage.foreignKeys.push(foreignKeyArgs)\n            })\n        }\n\n        // add unique metadata args from the schema\n        if (options.uniques) {\n            options.uniques.forEach((unique) => {\n                const uniqueArgs: UniqueMetadataArgs = {\n                    target: options.target || options.name,\n                    name: unique.name,\n                    columns: unique.columns,\n                    deferrable: unique.deferrable,\n                }\n                metadataArgsStorage.uniques.push(uniqueArgs)\n            })\n        }\n\n        // add check metadata args from the schema\n        if (options.checks) {\n            options.checks.forEach((check) => {\n                const checkArgs: CheckMetadataArgs = {\n                    target: options.target || options.name,\n                    name: check.name,\n                    expression: check.expression,\n                }\n                metadataArgsStorage.checks.push(checkArgs)\n            })\n        }\n\n        // add exclusion metadata args from the schema\n        if (options.exclusions) {\n            options.exclusions.forEach((exclusion) => {\n                const exclusionArgs: ExclusionMetadataArgs = {\n                    target: options.target || options.name,\n                    name: exclusion.name,\n                    expression: exclusion.expression,\n                }\n                metadataArgsStorage.exclusions.push(exclusionArgs)\n            })\n        }\n\n        if (options.embeddeds) {\n            Object.keys(options.embeddeds).forEach((columnName) => {\n                const embeddedOptions = options.embeddeds![columnName]\n\n                if (!embeddedOptions.schema)\n                    throw EntitySchemaEmbeddedError.createEntitySchemaIsRequiredException(\n                        columnName,\n                    )\n\n                const embeddedSchema = embeddedOptions.schema.options\n\n                metadataArgsStorage.embeddeds.push({\n                    target: options.target || options.name,\n                    propertyName: columnName,\n                    isArray: embeddedOptions.array === true,\n                    prefix:\n                        embeddedOptions.prefix !== undefined\n                            ? embeddedOptions.prefix\n                            : undefined,\n                    type: () => embeddedSchema?.target || embeddedSchema.name,\n                })\n\n                this.transformColumnsRecursive(\n                    embeddedSchema,\n                    metadataArgsStorage,\n                )\n            })\n        }\n    }\n}\n"], "sourceRoot": ".."}