{"version": 3, "sources": ["../../src/connection/ConnectionOptions.ts"], "names": [], "mappings": "", "file": "ConnectionOptions.js", "sourcesContent": ["import { DataSourceOptions } from \"../data-source/DataSourceOptions\"\n\n/**\n * ConnectionOptions is an interface with settings and options for specific connection.\n * Options contain database and other connection-related settings.\n * Consumer must provide connection options for each of your connections.\n *\n * @deprecated use DataSourceOptions instead\n */\nexport type ConnectionOptions = DataSourceOptions\n"], "sourceRoot": ".."}