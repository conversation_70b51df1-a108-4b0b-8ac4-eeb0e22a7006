{"version": 3, "sources": ["../../src/connection/ConnectionOptionsReader.ts"], "names": [], "mappings": ";;;;AAAA,0EAAuC;AACvC,wDAAuB;AAGvB,oCAAuC;AACvC,6DAAyD;AACzD,qDAAyD;AACzD,iDAA8C;AAC9C,4FAAwF;AAExF;;GAEG;AACH,MAAa,uBAAuB;IAChC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACc,OAWT;QAXS,YAAO,GAAP,OAAO,CAWhB;IACF,CAAC;IAEJ,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,GAAG;QACL,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAA;QACjC,IAAI,CAAC,OAAO;YACR,MAAM,IAAI,oBAAY,CAClB,kEAAkE,CACrE,CAAA;QAEL,OAAO,OAAO,CAAA;IAClB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,GAAG,CAAC,IAAY;QAClB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,CAAA;QACnC,MAAM,aAAa,GAAG,UAAU,CAAC,IAAI,CACjC,CAAC,OAAO,EAAE,EAAE,CACR,OAAO,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CACrE,CAAA;QACD,IAAI,CAAC,aAAa;YACd,MAAM,IAAI,oBAAY,CAClB,0BAA0B,IAAI,0DAA0D,CAC3F,CAAA;QAEL,OAAO,aAAa,CAAA;IACxB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CAAC,IAAY;QAClB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAA;QACpC,IAAI,CAAC,UAAU;YAAE,OAAO,KAAK,CAAA;QAE7B,MAAM,aAAa,GAAG,UAAU,CAAC,IAAI,CACjC,CAAC,OAAO,EAAE,EAAE,CACR,OAAO,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CACrE,CAAA;QACD,OAAO,CAAC,CAAC,aAAa,CAAA;IAC1B,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;;;OAIG;IACO,KAAK,CAAC,IAAI;QAChB,IAAI,iBAAiB,GAGH,SAAS,CAAA;QAE3B,MAAM,WAAW,GAAG;YAChB,KAAK;YACL,IAAI;YACJ,KAAK;YACL,KAAK;YACL,IAAI;YACJ,KAAK;YACL,KAAK;YACL,MAAM;SACT,CAAA;QAED,iDAAiD;QACjD,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAC9C,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CACrC,CAAA;QACD,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAClC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,SAAS,EAAE,KAAK,iBAAiB,CACvD,CAAA;QAED,qDAAqD;QACrD,MAAM,eAAe,GACjB,aAAa;YACb,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACxB,OAAO,6BAAa,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,GAAG,GAAG,GAAG,MAAM,CAAC,CAAA;YACpE,CAAC,CAAC,CAAA;QAEN,6BAA6B;QAC7B,MAAM,UAAU,GAAG,aAAa;YAC5B,CAAC,CAAC,IAAI,CAAC,YAAY;YACnB,CAAC,CAAC,IAAI,CAAC,YAAY,GAAG,GAAG,GAAG,eAAe,CAAA;QAE/C,uFAAuF;QACvF,IAAI,eAAe,KAAK,KAAK,EAAE,CAAC;YAC5B,6BAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;QACpC,CAAC;aAAM,IAAI,6BAAa,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,EAAE,CAAC;YAC/D,6BAAa,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,CAAA;QACtD,CAAC;QAED,gFAAgF;QAChF,IACI,6BAAa,CAAC,cAAc,CAAC,oBAAoB,CAAC;YAClD,6BAAa,CAAC,cAAc,CAAC,aAAa,CAAC,EAC7C,CAAC;YACC,iBAAiB,GAAG,MAAM,IAAI,uDAA0B,EAAE,CAAC,IAAI,EAAE,CAAA;QACrE,CAAC;aAAM,IACH,eAAe,KAAK,IAAI;YACxB,eAAe,KAAK,KAAK;YACzB,eAAe,KAAK,KAAK;YACzB,eAAe,KAAK,IAAI;YACxB,eAAe,KAAK,KAAK;YACzB,eAAe,KAAK,KAAK,EAC3B,CAAC;YACC,MAAM,CAAC,qBAAqB,EAAE,YAAY,CAAC,GACvC,MAAM,IAAA,iCAAmB,EAAC,UAAU,CAAC,CAAA;YACzC,MAAM,YAAY,GAAG,MAAM,qBAAqB,CAAA;YAEhD,IACI,YAAY,KAAK,KAAK;gBACtB,CAAC,YAAY;oBACT,YAAY,IAAI,YAAY;oBAC5B,SAAS,IAAI,YAAY,CAAC,EAChC,CAAC;gBACC,iBAAiB,GAAG,YAAY,CAAC,OAAO,CAAA;YAC5C,CAAC;iBAAM,CAAC;gBACJ,iBAAiB,GAAG,YAAY,CAAA;YACpC,CAAC;QACL,CAAC;aAAM,IAAI,eAAe,KAAK,MAAM,EAAE,CAAC;YACpC,iBAAiB,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;QAC3C,CAAC;QAED,0CAA0C;QAC1C,IAAI,iBAAiB,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,CAAA;QAC7D,CAAC;QAED,OAAO,SAAS,CAAA;IACpB,CAAC;IAED;;OAEG;IACO,0BAA0B,CAChC,iBAA0D;QAE1D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC;YACjC,iBAAiB,GAAG,CAAC,iBAAiB,CAAC,CAAA;QAE3C,iBAAiB,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAClC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAA;YAC1C,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACnB,MAAM,QAAQ,GAAI,OAAO,CAAC,QAAkB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;oBACxD,IACI,OAAO,MAAM,KAAK,QAAQ;wBAC1B,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG;wBAE3B,OAAO,IAAI,CAAC,aAAa,GAAG,GAAG,GAAG,MAAM,CAAA;oBAE5C,OAAO,MAAM,CAAA;gBACjB,CAAC,CAAC,CAAA;gBACF,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAA;YAC5D,CAAC;YACD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,WAAW,GAAI,OAAO,CAAC,WAAqB,CAAC,GAAG,CAClD,CAAC,UAAU,EAAE,EAAE;oBACX,IACI,OAAO,UAAU,KAAK,QAAQ;wBAC9B,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG;wBAE/B,OAAO,IAAI,CAAC,aAAa,GAAG,GAAG,GAAG,UAAU,CAAA;oBAEhD,OAAO,UAAU,CAAA;gBACrB,CAAC,CACJ,CAAA;gBACD,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC,CAAA;YAClE,CAAC;YACD,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,MAAM,UAAU,GAAI,OAAO,CAAC,UAAoB,CAAC,GAAG,CAChD,CAAC,SAAS,EAAE,EAAE;oBACV,IACI,OAAO,SAAS,KAAK,QAAQ;wBAC7B,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG;wBAE9B,OAAO,IAAI,CAAC,aAAa,GAAG,GAAG,GAAG,SAAS,CAAA;oBAE/C,OAAO,SAAS,CAAA;gBACpB,CAAC,CACJ,CAAA;gBACD,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAA;YAChE,CAAC;YAED,6DAA6D;YAC7D,IACI,OAAO,CAAC,IAAI,KAAK,QAAQ;gBACzB,OAAO,CAAC,IAAI,KAAK,gBAAgB,EACnC,CAAC;gBACC,IACI,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ;oBACpC,CAAC,IAAA,sBAAU,EAAC,OAAO,CAAC,QAAQ,CAAC;oBAC7B,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,IAAI,gBAAgB;oBACzD,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,IAAI,mBAAmB;oBAC9D,OAAO,CAAC,QAAQ,KAAK,UAAU,EACjC,CAAC;oBACC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;wBACnB,QAAQ,EAAE,IAAI,CAAC,aAAa,GAAG,GAAG,GAAG,OAAO,CAAC,QAAQ;qBACxD,CAAC,CAAA;gBACN,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAA;QAEF,OAAO,iBAAiB,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,IAAc,YAAY;QACtB,OAAO,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,IAAc,aAAa;QACvB,OAAO,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,uBAAW,CAAC,IAAI,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,IAAc,cAAc;QACxB,OAAO,IAAI,CAAC,OAAO,EAAE,UAAU,IAAI,WAAW,CAAA;IAClD,CAAC;CACJ;AAhQD,0DAgQC", "file": "ConnectionOptionsReader.js", "sourcesContent": ["import appRootPath from \"app-root-path\"\nimport path from \"path\"\n\nimport { DataSourceOptions } from \"../data-source/DataSourceOptions\"\nimport { TypeORMError } from \"../error\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { importOrRequireFile } from \"../util/ImportUtils\"\nimport { isAbsolute } from \"../util/PathUtils\"\nimport { ConnectionOptionsEnvReader } from \"./options-reader/ConnectionOptionsEnvReader\"\n\n/**\n * Reads connection options from the ormconfig.\n */\nexport class ConnectionOptionsReader {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        protected options?: {\n            /**\n             * Directory where ormconfig should be read from.\n             * By default its your application root (where your app package.json is located).\n             */\n            root?: string\n\n            /**\n             * Filename of the ormconfig configuration. By default its equal to \"ormconfig\".\n             */\n            configName?: string\n        },\n    ) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Returns all connection options read from the ormconfig.\n     */\n    async all(): Promise<DataSourceOptions[]> {\n        const options = await this.load()\n        if (!options)\n            throw new TypeORMError(\n                `No connection options were found in any orm configuration files.`,\n            )\n\n        return options\n    }\n\n    /**\n     * Gets a connection with a given name read from ormconfig.\n     * If connection with such name would not be found then it throw error.\n     */\n    async get(name: string): Promise<DataSourceOptions> {\n        const allOptions = await this.all()\n        const targetOptions = allOptions.find(\n            (options) =>\n                options.name === name || (name === \"default\" && !options.name),\n        )\n        if (!targetOptions)\n            throw new TypeORMError(\n                `Cannot find connection ${name} because its not defined in any orm configuration files.`,\n            )\n\n        return targetOptions\n    }\n\n    /**\n     * Checks if there is a TypeORM configuration file.\n     */\n    async has(name: string): Promise<boolean> {\n        const allOptions = await this.load()\n        if (!allOptions) return false\n\n        const targetOptions = allOptions.find(\n            (options) =>\n                options.name === name || (name === \"default\" && !options.name),\n        )\n        return !!targetOptions\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Loads all connection options from a configuration file.\n     *\n     * todo: get in count NODE_ENV somehow\n     */\n    protected async load(): Promise<DataSourceOptions[] | undefined> {\n        let connectionOptions:\n            | DataSourceOptions\n            | DataSourceOptions[]\n            | undefined = undefined\n\n        const fileFormats = [\n            \"env\",\n            \"js\",\n            \"mjs\",\n            \"cjs\",\n            \"ts\",\n            \"mts\",\n            \"cts\",\n            \"json\",\n        ]\n\n        // Detect if baseFilePath contains file extension\n        const possibleExtension = this.baseFilePath.substr(\n            this.baseFilePath.lastIndexOf(\".\"),\n        )\n        const fileExtension = fileFormats.find(\n            (extension) => `.${extension}` === possibleExtension,\n        )\n\n        // try to find any of following configuration formats\n        const foundFileFormat =\n            fileExtension ||\n            fileFormats.find((format) => {\n                return PlatformTools.fileExist(this.baseFilePath + \".\" + format)\n            })\n\n        // Determine config file name\n        const configFile = fileExtension\n            ? this.baseFilePath\n            : this.baseFilePath + \".\" + foundFileFormat\n\n        // if .env file found then load all its variables into process.env using dotenv package\n        if (foundFileFormat === \"env\") {\n            PlatformTools.dotenv(configFile)\n        } else if (PlatformTools.fileExist(this.baseDirectory + \"/.env\")) {\n            PlatformTools.dotenv(this.baseDirectory + \"/.env\")\n        }\n\n        // try to find connection options from any of available sources of configuration\n        if (\n            PlatformTools.getEnvVariable(\"TYPEORM_CONNECTION\") ||\n            PlatformTools.getEnvVariable(\"TYPEORM_URL\")\n        ) {\n            connectionOptions = await new ConnectionOptionsEnvReader().read()\n        } else if (\n            foundFileFormat === \"js\" ||\n            foundFileFormat === \"mjs\" ||\n            foundFileFormat === \"cjs\" ||\n            foundFileFormat === \"ts\" ||\n            foundFileFormat === \"mts\" ||\n            foundFileFormat === \"cts\"\n        ) {\n            const [importOrRequireResult, moduleSystem] =\n                await importOrRequireFile(configFile)\n            const configModule = await importOrRequireResult\n\n            if (\n                moduleSystem === \"esm\" ||\n                (configModule &&\n                    \"__esModule\" in configModule &&\n                    \"default\" in configModule)\n            ) {\n                connectionOptions = configModule.default\n            } else {\n                connectionOptions = configModule\n            }\n        } else if (foundFileFormat === \"json\") {\n            connectionOptions = require(configFile)\n        }\n\n        // normalize and return connection options\n        if (connectionOptions) {\n            return this.normalizeConnectionOptions(connectionOptions)\n        }\n\n        return undefined\n    }\n\n    /**\n     * Normalize connection options.\n     */\n    protected normalizeConnectionOptions(\n        connectionOptions: DataSourceOptions | DataSourceOptions[],\n    ): DataSourceOptions[] {\n        if (!Array.isArray(connectionOptions))\n            connectionOptions = [connectionOptions]\n\n        connectionOptions.forEach((options) => {\n            options.baseDirectory = this.baseDirectory\n            if (options.entities) {\n                const entities = (options.entities as any[]).map((entity) => {\n                    if (\n                        typeof entity === \"string\" &&\n                        entity.substr(0, 1) !== \"/\"\n                    )\n                        return this.baseDirectory + \"/\" + entity\n\n                    return entity\n                })\n                Object.assign(connectionOptions, { entities: entities })\n            }\n            if (options.subscribers) {\n                const subscribers = (options.subscribers as any[]).map(\n                    (subscriber) => {\n                        if (\n                            typeof subscriber === \"string\" &&\n                            subscriber.substr(0, 1) !== \"/\"\n                        )\n                            return this.baseDirectory + \"/\" + subscriber\n\n                        return subscriber\n                    },\n                )\n                Object.assign(connectionOptions, { subscribers: subscribers })\n            }\n            if (options.migrations) {\n                const migrations = (options.migrations as any[]).map(\n                    (migration) => {\n                        if (\n                            typeof migration === \"string\" &&\n                            migration.substr(0, 1) !== \"/\"\n                        )\n                            return this.baseDirectory + \"/\" + migration\n\n                        return migration\n                    },\n                )\n                Object.assign(connectionOptions, { migrations: migrations })\n            }\n\n            // make database path file in sqlite relative to package.json\n            if (\n                options.type === \"sqlite\" ||\n                options.type === \"better-sqlite3\"\n            ) {\n                if (\n                    typeof options.database === \"string\" &&\n                    !isAbsolute(options.database) &&\n                    options.database.substr(0, 1) !== \"/\" && // unix absolute\n                    options.database.substr(1, 2) !== \":\\\\\" && // windows absolute\n                    options.database !== \":memory:\"\n                ) {\n                    Object.assign(options, {\n                        database: this.baseDirectory + \"/\" + options.database,\n                    })\n                }\n            }\n        })\n\n        return connectionOptions\n    }\n\n    /**\n     * Gets directory where configuration file should be located and configuration file name.\n     */\n    protected get baseFilePath(): string {\n        return path.resolve(this.baseDirectory, this.baseConfigName)\n    }\n\n    /**\n     * Gets directory where configuration file should be located.\n     */\n    protected get baseDirectory(): string {\n        return this.options?.root ?? appRootPath.path\n    }\n\n    /**\n     * Gets configuration file name.\n     */\n    protected get baseConfigName(): string {\n        return this.options?.configName ?? \"ormconfig\"\n    }\n}\n"], "sourceRoot": ".."}