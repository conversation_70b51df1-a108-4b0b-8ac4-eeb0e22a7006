import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { ClientKafka } from '@nestjs/microservices';
import { v4 as uuidv4 } from 'uuid';
import { Course } from './entities/course.entity';
import { Order } from './entities/order.entity';
import { CourseRepository, OrderRepository } from './repositories';

@Injectable()
export class AppService implements OnModuleInit {
  constructor(
    @Inject('KAFKA_SERVICE') private readonly kafkaClient: ClientKafka,
    private readonly courseRepository: CourseRepository,
    private readonly orderRepository: OrderRepository,
  ) {}

  async onModuleInit() {
    try {
      this.kafkaClient.subscribeToResponseOf('order.created');
      await this.kafkaClient.connect();
      console.log('✅ Kafka connected successfully');
    } catch (error) {
      console.error('❌ Failed to connect to Kafka:', error.message);
      // Continue without <PERSON><PERSON><PERSON> for development
    }

    // Seed default courses
    await this.courseRepository.seedDefaultCourses();
  }

  async createOrder(userId: string, courseId: string): Promise<Order> {
    const course = await this.courseRepository.findById(courseId);
    if (!course) {
      throw new Error('Course not found');
    }

    const orderData = {
      id: uuidv4(),
      userId,
      courseId,
      status: 'pending' as const,
      createdAt: new Date(),
    };

    const order = await this.orderRepository.create(orderData);

    // Emit order created event to Kafka
    try {
      await this.kafkaClient.emit('order.created', {
        orderId: order.id,
        userId: order.userId,
        courseId: order.courseId,
        timestamp: order.createdAt,
      });
      console.log('✅ Order event emitted to Kafka:', order.id);
    } catch (error) {
      console.error('❌ Failed to emit order event:', error.message);
      // Continue without Kafka for development
    }

    return order;
  }

  async getOrders(): Promise<Order[]> {
    return await this.orderRepository.findAll();
  }

  async getCourses(): Promise<Course[]> {
    return await this.courseRepository.findAll();
  }
}
