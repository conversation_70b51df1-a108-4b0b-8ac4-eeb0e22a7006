import { <PERSON><PERSON>ty, <PERSON>umn, PrimaryColumn, ManyToOne, <PERSON>inC<PERSON>umn } from 'typeorm';
import { Course } from './course.entity';

@Entity('orders')
export class Order {
  @PrimaryColumn()
  id: string;

  @Column()
  userId: string;

  @Column()
  courseId: string;

  @Column({
    type: 'varchar',
    enum: ['pending', 'completed'],
    default: 'pending'
  })
  status: 'pending' | 'completed';

  @Column()
  createdAt: Date;

  @ManyToOne(() => Course, course => course.orders)
  @JoinColumn({ name: 'courseId' })
  course: Course;
}
