import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { Course, Order } from './entities';
import { CourseRepository, OrderRepository } from './repositories';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'sqlite',
      database: process.env.NODE_ENV === 'production' ? '/app/order.db' : 'order.db',
      entities: [Course, Order],
      synchronize: true, // Only for development
      logging: true,
    }),
    TypeOrmModule.forFeature([Course, Order]),
    ClientsModule.register([
      {
        name: 'KAFKA_SERVICE',
        transport: Transport.KAFKA,
        options: {
          client: {
            clientId: 'order',
            brokers: ['localhost:29092', 'localhost:29093', 'localhost:29094'],
          },
          consumer: {
            groupId: 'order-consumer',
          },
        },
      },
    ]),
  ],
  controllers: [AppController],
  providers: [AppService, CourseRepository, OrderRepository],
})
export class AppModule {}
