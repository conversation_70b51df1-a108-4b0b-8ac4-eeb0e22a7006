import { Controller, Get, Post, Body } from '@nestjs/common';
import { AppService } from './app.service';
import { Course } from './entities/course.entity';
import { Order } from './entities/order.entity';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Post('orders')
  async createOrder(
    @Body() createOrderDto: { userId: string; courseId: string },
  ): Promise<Order> {
    return this.appService.createOrder(createOrderDto.userId, createOrderDto.courseId);
  }

  @Get('orders')
  async getOrders(): Promise<Order[]> {
    return await this.appService.getOrders();
  }

  @Get('courses')
  async getCourses(): Promise<Course[]> {
    return await this.appService.getCourses();
  }
}
