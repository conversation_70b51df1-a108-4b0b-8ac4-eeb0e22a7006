import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order } from '../entities/order.entity';

@Injectable()
export class OrderRepository {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepo: Repository<Order>,
  ) {}

  async create(orderData: Partial<Order>): Promise<Order> {
    const order = this.orderRepo.create(orderData);
    return await this.orderRepo.save(order);
  }

  async findAll(): Promise<Order[]> {
    return await this.orderRepo.find({
      relations: ['course'],
    });
  }

  async findById(id: string): Promise<Order | null> {
    return await this.orderRepo.findOne({
      where: { id },
      relations: ['course'],
    });
  }

  async findByUserId(userId: string): Promise<Order[]> {
    return await this.orderRepo.find({
      where: { userId },
      relations: ['course'],
    });
  }

  async update(id: string, updateData: Partial<Order>): Promise<Order | null> {
    await this.orderRepo.update(id, updateData);
    return await this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.orderRepo.delete(id);
  }
}
