import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Course } from '../entities/course.entity';

@Injectable()
export class CourseRepository {
  constructor(
    @InjectRepository(Course)
    private readonly courseRepo: Repository<Course>,
  ) {}

  async create(courseData: Partial<Course>): Promise<Course> {
    const course = this.courseRepo.create(courseData);
    return await this.courseRepo.save(course);
  }

  async findAll(): Promise<Course[]> {
    return await this.courseRepo.find();
  }

  async findById(id: string): Promise<Course | null> {
    return await this.courseRepo.findOne({ where: { id } });
  }

  async update(id: string, updateData: Partial<Course>): Promise<Course | null> {
    await this.courseRepo.update(id, updateData);
    return await this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.courseRepo.delete(id);
  }

  async seedDefaultCourses(): Promise<void> {
    const existingCourses = await this.findAll();
    if (existingCourses.length === 0) {
      const defaultCourses = [
        { id: '1', name: 'NestJS Course', price: 100 },
        { id: '2', name: 'Kafka Course', price: 150 },
      ];

      for (const courseData of defaultCourses) {
        await this.create(courseData);
      }
    }
  }
}
