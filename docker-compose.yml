version: '3.8'

services:
  # Order Service
  order-service:
    build:
      context: ./order-service
      dockerfile: Dockerfile
    container_name: order-service
    # ports:
    #   - "3000:3000"
    environment:
      - NODE_ENV=production
      - KAFKA_BROKERS=kafka1:29092,kafka2:29093,kafka3:29094
    volumes:
      - ./order-service:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - nginx_network

  # Campaign Service
  campaign-service:
    build:
      context: ./campaign-service
      dockerfile: Dockerfile
    container_name: campaign-service
    # ports:
    #   - "3001:3001"
    environment:
      - NODE_ENV=production
      - KAFKA_BROKERS=kafka1:29092,kafka2:29093,kafka3:29094
    volumes:
      - ./campaign-service:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - nginx_network

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: api-gateway
    depends_on:
      - order-service
      - campaign-service
    # ports:
    #   - "3002:3002"
    environment:
      - NODE_ENV=production
      - ORDER_SERVICE_URL=http://order-service:3000
      - CAMPAIGN_SERVICE_URL=http://campaign-service:3001
    volumes:
      - ./api-gateway:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - nginx_network

networks:
  nginx_network:
    name: nginx_network
    driver: bridge
