# Docker Host IP (for external access to Kafka)
DOCKER_HOST_IP=127.0.0.1

# Kafka Configuration
KAFKA_BROKERS=kafka1:29092,kafka2:29093,kafka3:29094

# Service URLs (for API Gateway)
ORDER_SERVICE_URL=http://order-service:3000
CAMPAIGN_SERVICE_URL=http://campaign-service:3001

# Database Configuration
NODE_ENV=production

# Ports
ORDER_SERVICE_PORT=3000
CAMPAIGN_SERVICE_PORT=3001
API_GATEWAY_PORT=3002
KAFKA_UI_PORT=8080

# Kafka Ports
KAFKA1_PORT=9092
KAFKA2_PORT=9093
KAFKA3_PORT=9094
