"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
let AppService = class AppService {
    constructor(httpService, serviceConfig) {
        this.httpService = httpService;
        this.serviceConfig = serviceConfig;
    }
    async proxyToOrderService(path, method, body) {
        const url = `${this.serviceConfig.order.url}${path}`;
        const response = await (0, rxjs_1.firstValueFrom)(this.httpService.request({
            method,
            url,
            data: body,
        }));
        return response.data;
    }
    async proxyToCampaignService(path, method, body) {
        const url = `${this.serviceConfig.campaign.url}${path}`;
        const response = await (0, rxjs_1.firstValueFrom)(this.httpService.request({
            method,
            url,
            data: body,
        }));
        return response.data;
    }
};
exports.AppService = AppService;
exports.AppService = AppService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)('SERVICE_CONFIG')),
    __metadata("design:paramtypes", [axios_1.HttpService, Object])
], AppService);
//# sourceMappingURL=app.service.js.map