import { AppService } from './app.service';
import { Request } from 'express';
export declare class AppController {
    private readonly appService;
    constructor(appService: AppService);
    getCourses(): Promise<any>;
    getOrders(): Promise<any>;
    createOrder(body: any): Promise<any>;
    getCampaigns(): Promise<any>;
    createCampaign(body: any): Promise<any>;
    getUserPoints(userId: string): Promise<any>;
    handleFallback(request: Request): Promise<any>;
}
