import { HttpService } from '@nestjs/axios';
import { ServiceConfig } from './interfaces/service.interface';
export declare class AppService {
    private readonly httpService;
    private readonly serviceConfig;
    constructor(httpService: HttpService, serviceConfig: ServiceConfig);
    proxyToOrderService(path: string, method: string, body?: any): Promise<any>;
    proxyToCampaignService(path: string, method: string, body?: any): Promise<any>;
}
