import { Controller, Get, Post, Body, Param, All, Req } from '@nestjs/common';
import { AppService } from './app.service';
import { Request } from 'express';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  // Order Service Routes
  @Get('courses')
  async getCourses() {
    return this.appService.proxyToOrderService('/courses', 'GET');
  }

  @Get('orders')
  async getOrders() {
    return this.appService.proxyToOrderService('/orders', 'GET');
  }

  @Post('orders')
  async createOrder(@Body() body: any) {
    return this.appService.proxyToOrderService('/orders', 'POST', body);
  }

  // Campaign Service Routes
  @Get('campaigns')
  async getCampaigns() {
    return this.appService.proxyToCampaignService('/campaigns', 'GET');
  }

  @Post('campaigns')
  async createCampaign(@Body() body: any) {
    return this.appService.proxyToCampaignService('/campaigns', 'POST', body);
  }

  @Get('points/:userId')
  async getUserPoints(@Param('userId') userId: string) {
    return this.appService.proxyToCampaignService(`/points/${userId}`, 'GET');
  }

  // Fallback route handler
  @All('*')
  async handleFallback(@Req() request: Request) {
    const { path, method } = request;
    if (path.startsWith('/orders') || path.startsWith('/courses')) {
      return this.appService.proxyToOrderService(path, method, request.body);
    } else if (path.startsWith('/campaigns') || path.startsWith('/points')) {
      return this.appService.proxyToCampaignService(path, method, request.body);
    }
    throw new Error('Route not found');
  }
}
