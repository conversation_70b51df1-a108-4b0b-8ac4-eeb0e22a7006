import { Inject, Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ServiceConfig } from './interfaces/service.interface';

@Injectable()
export class AppService {
  constructor(
    private readonly httpService: HttpService,
    @Inject('SERVICE_CONFIG') private readonly serviceConfig: ServiceConfig,
  ) {}

  async proxyToOrderService(path: string, method: string, body?: any) {
    const url = `${this.serviceConfig.order.url}${path}`;
    const response = await firstValueFrom(
      this.httpService.request({
        method,
        url,
        data: body,
      }),
    );
    return response.data;
  }

  async proxyToCampaignService(path: string, method: string, body?: any) {
    const url = `${this.serviceConfig.campaign.url}${path}`;
    const response = await firstValueFrom(
      this.httpService.request({
        method,
        url,
        data: body,
      }),
    );
    return response.data;
  }
}
