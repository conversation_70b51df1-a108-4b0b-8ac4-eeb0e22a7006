# Re-enabling Kafka

Để bật lại Kafka messaging, bạn cần uncomment các dòng code đã được comment:

## Order Service

### File: `order-service/src/app.service.ts`

```typescript
// Uncomment these lines:
async onModuleInit() {
  this.kafkaClient.subscribeToResponseOf('order.created');
  await this.kafkaClient.connect();
  
  // Seed default courses
  await this.courseRepository.seedDefaultCourses();
}

// And in createOrder method:
await this.kafkaClient.emit('order.created', {
  orderId: order.id,
  userId: order.userId,
  courseId: order.courseId,
  timestamp: order.createdAt,
});
```

## Campaign Service

### File: `campaign-service/src/main.ts`

```typescript
// Uncomment these lines:
app.connectMicroservice<MicroserviceOptions>({
  transport: Transport.KAFKA,
  options: {
    client: {
      brokers: ['localhost:9092'],
    },
    consumer: {
      groupId: 'campaign-consumer',
    },
  },
});

await app.startAllMicroservices();
```

## Kafka Setup

Trước khi enable Kafka, đảm bảo Kafka server đang chạy:

```bash
# Start Kafka (nếu sử dụng Docker)
docker run -d --name kafka -p 9092:9092 apache/kafka:latest

# Hoặc start Kafka locally nếu đã cài đặt
```

## Test Full Flow

Sau khi enable Kafka:

1. Start cả hai services
2. Tạo một campaign
3. Tạo một order
4. Kiểm tra user points sẽ được tự động tạo thông qua Kafka messaging

```bash
# 1. Create campaign
curl -X POST http://localhost:3001/campaigns \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Campaign",
    "totalPoints": 1000,
    "startDate": "2025-01-01T00:00:00.000Z",
    "endDate": "2025-12-31T23:59:59.000Z"
  }'

# 2. Create order (will trigger Kafka message)
curl -X POST http://localhost:3000/orders \
  -H "Content-Type: application/json" \
  -d '{"userId": "user123", "courseId": "1"}'

# 3. Check user points (should have 10 points)
curl http://localhost:3001/points/user123
```
