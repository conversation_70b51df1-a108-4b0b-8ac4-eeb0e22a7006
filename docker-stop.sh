#!/bin/bash

echo "🛑 Stopping Kafka Cluster and Microservices..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

echo -e "${YELLOW}Stopping all services...${NC}"

# Stop all services
docker-compose down

if [ $? -eq 0 ]; then
    print_status 0 "All services stopped successfully"
else
    print_status 1 "Failed to stop some services"
fi

echo ""
echo -e "${YELLOW}Cleaning up...${NC}"

# Remove unused containers, networks, images
docker system prune -f

print_status 0 "Cleanup completed"

echo ""
echo -e "${GREEN}🎉 All services stopped and cleaned up!${NC}"
echo ""
echo -e "${YELLOW}💡 To remove volumes (delete databases):${NC}"
echo "  docker-compose down -v"
echo ""
echo -e "${YELLOW}💡 To restart services:${NC}"
echo "  ./docker-start.sh"
