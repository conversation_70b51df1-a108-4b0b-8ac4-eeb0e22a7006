# Docker Setup với Kafka Cluster

## 🐳 Tổng quan

Setup này bao gồm:
- **Kafka Cluster**: 3 brokers (kafka1, kafka2, kafka3) với Zookeeper
- **Order Service**: Port 3000 với SQLite database
- **Campaign Service**: Port 3001 với SQLite database  
- **API Gateway**: Port 3002
- **Kafka UI**: Port 8080 để quản lý Kafka

## 🚀 Khởi động nhanh

### 1. Start tất cả services:
```bash
./docker-start.sh
```

### 2. Test Kafka flow:
```bash
./test-kafka-flow.sh
```

### 3. Stop tất cả services:
```bash
./docker-stop.sh
```

## 📊 Services và Ports

| Service | Port | URL | Mô tả |
|---------|------|-----|-------|
| Order Service | 3000 | http://localhost:3000 | Quản lý orders và courses |
| Campaign Service | 3001 | http://localhost:3001 | Quản lý campaigns và points |
| API Gateway | 3002 | http://localhost:3002 | Gateway cho tất cả services |
| Kafka UI | 8080 | http://localhost:8080 | Web UI để quản lý Kafka |
| Kafka1 | 9092 | localhost:9092 | Kafka broker 1 |
| Kafka2 | 9093 | localhost:9093 | Kafka broker 2 |
| Kafka3 | 9094 | localhost:9094 | Kafka broker 3 |

## 🔧 Cấu hình Kafka

### Multi-broker Setup:
- **3 Kafka brokers** cho high availability
- **Replication factor**: 3
- **Min ISR**: 2
- **Auto-create topics**: Enabled

### Broker Configuration:
```yaml
kafka1: kafka1:29092 (internal), localhost:9092 (external)
kafka2: kafka2:29093 (internal), localhost:9093 (external)  
kafka3: kafka3:29094 (internal), localhost:9094 (external)
```

## 📡 Kafka Topics

| Topic | Partitions | Replication | Producer | Consumer |
|-------|------------|-------------|----------|----------|
| order.created | 3 | 3 | Order Service | Campaign Service |

## 🗄️ Database Volumes

- **order-db**: SQLite database cho Order Service
- **campaign-db**: SQLite database cho Campaign Service
- **kafka1-data, kafka2-data, kafka3-data**: Kafka data persistence
- **zookeeper-data, zookeeper-logs**: Zookeeper persistence

## 🧪 Testing APIs

### Order Service APIs:
```bash
# Get courses
curl http://localhost:3000/courses

# Create order
curl -X POST http://localhost:3000/orders \
  -H "Content-Type: application/json" \
  -d '{"userId": "user123", "courseId": "1"}'

# Get orders
curl http://localhost:3000/orders
```

### Campaign Service APIs:
```bash
# Create campaign
curl -X POST http://localhost:3001/campaigns \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Campaign",
    "totalPoints": 1000,
    "startDate": "2025-01-01T00:00:00.000Z",
    "endDate": "2025-12-31T23:59:59.000Z"
  }'

# Get campaigns
curl http://localhost:3001/campaigns

# Get user points
curl http://localhost:3001/points/user123
```

## 🔍 Monitoring và Debugging

### Xem logs:
```bash
# Tất cả services
docker-compose logs -f

# Specific service
docker-compose logs -f order-service
docker-compose logs -f campaign-service
docker-compose logs -f kafka1

# Kafka UI
docker-compose logs -f kafka-ui
```

### Check service status:
```bash
docker-compose ps
```

### Kafka UI Dashboard:
- URL: http://localhost:8080
- Xem topics, messages, consumers
- Monitor cluster health

## 🛠️ Development Commands

### Rebuild specific service:
```bash
docker-compose up --build order-service
docker-compose up --build campaign-service
```

### Scale Kafka consumers:
```bash
docker-compose up --scale campaign-service=2
```

### Reset databases:
```bash
docker-compose down -v  # Removes volumes
./docker-start.sh       # Restart with fresh DBs
```

## 🔧 Environment Variables

Copy `.env.example` to `.env` và customize:
```bash
cp .env.example .env
```

Key variables:
- `DOCKER_HOST_IP`: IP cho external Kafka access
- `KAFKA_BROKERS`: Kafka broker addresses
- `NODE_ENV`: Environment (production/development)

## 🚨 Troubleshooting

### Kafka connection issues:
1. Check if all Kafka brokers are running: `docker-compose ps`
2. Check Kafka logs: `docker-compose logs kafka1 kafka2 kafka3`
3. Verify network connectivity: `docker network ls`

### Service startup issues:
1. Check service logs: `docker-compose logs [service-name]`
2. Verify port availability: `netstat -tulpn | grep [port]`
3. Rebuild images: `docker-compose up --build`

### Database issues:
1. Check volume mounts: `docker volume ls`
2. Reset databases: `docker-compose down -v && ./docker-start.sh`

## 📈 Production Considerations

1. **Security**: Add authentication cho Kafka và services
2. **Monitoring**: Integrate với Prometheus/Grafana
3. **Logging**: Centralized logging với ELK stack
4. **Backup**: Database backup strategy
5. **Scaling**: Horizontal scaling cho services
6. **Load Balancing**: Nginx/HAProxy cho API Gateway

## 🎉 Kết luận

Setup này cung cấp:
- ✅ High-availability Kafka cluster
- ✅ Persistent SQLite databases
- ✅ Microservices architecture
- ✅ Easy development workflow
- ✅ Comprehensive monitoring tools
