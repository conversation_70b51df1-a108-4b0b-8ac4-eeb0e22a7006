import { NestFactory } from '@nestjs/core';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Configure Kafka consumer
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.KAFKA,
    options: {
      client: {
        brokers: process.env.KAFKA_BROKERS?.split(',') || [
          'kafka1:29092',
          'kafka2:29093',
          'kafka3:29094'
        ],
      },
      consumer: {
        groupId: 'campaign-consumer',
      },
    },
  });

  try {
    await app.startAllMicroservices();
    console.log('✅ Campaign microservice started successfully');
  } catch (error) {
    console.error('❌ Failed to start microservice:', error.message);
    // Continue without Kafka for development
  }

  await app.listen(3001);
}
bootstrap();
