import { NestFactory } from '@nestjs/core';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { AppModule } from './app.module';
import * as dotenvFlow from 'dotenv-flow';

async function bootstrap() {
  // Load environment variables based on NODE_ENV
  dotenvFlow.config();
  const app = await NestFactory.create(AppModule);

  // Configure Kafka consumer
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.KAFKA,
    options: {
      client: {
        brokers: ['localhost:29092', 'localhost:29093', 'localhost:29094'],
      },
      consumer: {
        groupId: 'campaign-consumer',
      },
    },
  });

  try {
    await app.startAllMicroservices();
    console.log('✅ Campaign microservice started successfully');
  } catch (error) {
    console.error('❌ Failed to start microservice:', error.message);
    // Continue without <PERSON><PERSON><PERSON> for development
  }

  await app.listen(3001);
}
bootstrap();
