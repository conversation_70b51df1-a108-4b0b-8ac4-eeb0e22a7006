import { <PERSON>, Post, Body, Get, Param } from '@nestjs/common';
import { EventPattern } from '@nestjs/microservices';
import { AppService } from './app.service';
import { Campaign } from './entities/campaign.entity';
import { UserPoints } from './entities/user-points.entity';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Post('campaigns')
  async createCampaign(
    @Body() createCampaignDto: {
      name: string;
      totalPoints: number;
      startDate: string;
      endDate: string;
    },
  ): Promise<Campaign> {
    return await this.appService.createCampaign(
      createCampaignDto.name,
      createCampaignDto.totalPoints,
      new Date(createCampaignDto.startDate),
      new Date(createCampaignDto.endDate),
    );
  }

  @Get('campaigns')
  async getCampaigns(): Promise<Campaign[]> {
    return await this.appService.getCampaigns();
  }

  @Get('points/:userId')
  async getUserPoints(@Param('userId') userId: string): Promise<UserPoints[]> {
    return await this.appService.getUserPoints(userId);
  }

  @EventPattern('order.created')
  async handleOrderCreated(data: { 
    orderId: string; 
    userId: string; 
    timestamp: string;
  }) {
    return this.appService.handleOrderCreated({
      ...data,
      timestamp: new Date(data.timestamp),
    });
  }
}
