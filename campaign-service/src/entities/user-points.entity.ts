import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Campaign } from './campaign.entity';

@Entity('user_points')
export class UserPoints {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: string;

  @Column()
  campaignId: string;

  @Column()
  points: number;

  @Column()
  orderId: string;

  @Column()
  timestamp: Date;

  @ManyToOne(() => Campaign, campaign => campaign.userPoints)
  @JoinColumn({ name: 'campaignId' })
  campaign: Campaign;
}
