import { <PERSON><PERSON>ty, Column, PrimaryColumn, OneToMany } from 'typeorm';
import { UserPoints } from './user-points.entity';

@Entity('campaigns')
export class Campaign {
  @PrimaryColumn()
  id: string;

  @Column()
  name: string;

  @Column()
  totalPoints: number;

  @Column()
  remainingPoints: number;

  @Column()
  startDate: Date;

  @Column()
  endDate: Date;

  @Column({
    type: 'varchar',
    enum: ['active', 'completed'],
    default: 'active'
  })
  status: 'active' | 'completed';

  @OneToMany(() => UserPoints, userPoints => userPoints.campaign)
  userPoints: UserPoints[];
}
