import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Campaign } from '../entities/campaign.entity';

@Injectable()
export class CampaignRepository {
  constructor(
    @InjectRepository(Campaign)
    private readonly campaignRepo: Repository<Campaign>,
  ) {}

  async create(campaignData: Partial<Campaign>): Promise<Campaign> {
    const campaign = this.campaignRepo.create(campaignData);
    return await this.campaignRepo.save(campaign);
  }

  async findAll(): Promise<Campaign[]> {
    return await this.campaignRepo.find();
  }

  async findById(id: string): Promise<Campaign | null> {
    return await this.campaignRepo.findOne({ where: { id } });
  }

  async findActiveCampaign(timestamp: Date): Promise<Campaign | null> {
    return await this.campaignRepo
      .createQueryBuilder('campaign')
      .where('campaign.status = :status', { status: 'active' })
      .andWhere('campaign.startDate <= :timestamp', { timestamp })
      .andWhere('campaign.endDate >= :timestamp', { timestamp })
      .andWhere('campaign.remainingPoints > 0')
      .getOne();
  }

  async update(id: string, updateData: Partial<Campaign>): Promise<Campaign | null> {
    await this.campaignRepo.update(id, updateData);
    return await this.findById(id);
  }

  async save(campaign: Campaign): Promise<Campaign> {
    return await this.campaignRepo.save(campaign);
  }
}
