import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserPoints } from '../entities/user-points.entity';

@Injectable()
export class UserPointsRepository {
  constructor(
    @InjectRepository(UserPoints)
    private readonly userPointsRepo: Repository<UserPoints>,
  ) {}

  async create(userPointsData: Partial<UserPoints>): Promise<UserPoints> {
    const userPoints = this.userPointsRepo.create(userPointsData);
    return await this.userPointsRepo.save(userPoints);
  }

  async findByUserId(userId: string): Promise<UserPoints[]> {
    return await this.userPointsRepo.find({
      where: { userId },
      relations: ['campaign'],
    });
  }

  async findByCampaignId(campaignId: string): Promise<UserPoints[]> {
    return await this.userPointsRepo.find({
      where: { campaignId },
    });
  }

  async findAll(): Promise<UserPoints[]> {
    return await this.userPointsRepo.find({
      relations: ['campaign'],
    });
  }
}
