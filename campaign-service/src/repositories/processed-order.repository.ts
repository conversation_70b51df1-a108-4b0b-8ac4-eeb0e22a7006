import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProcessedOrder } from '../entities/processed-order.entity';

@Injectable()
export class ProcessedOrderRepository {
  constructor(
    @InjectRepository(ProcessedOrder)
    private readonly processedOrderRepo: Repository<ProcessedOrder>,
  ) {}

  async create(orderId: string): Promise<ProcessedOrder> {
    const processedOrder = this.processedOrderRepo.create({
      orderId,
      processedAt: new Date(),
    });
    return await this.processedOrderRepo.save(processedOrder);
  }

  async exists(orderId: string): Promise<boolean> {
    const count = await this.processedOrderRepo.count({
      where: { orderId },
    });
    return count > 0;
  }

  async findAll(): Promise<ProcessedOrder[]> {
    return await this.processedOrderRepo.find();
  }
}
