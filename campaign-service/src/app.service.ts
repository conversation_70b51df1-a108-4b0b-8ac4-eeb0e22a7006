import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { Campaign } from './entities/campaign.entity';
import { UserPoints } from './entities/user-points.entity';
import { CampaignRepository, UserPointsRepository, ProcessedOrderRepository } from './repositories';

@Injectable()
export class AppService {
  constructor(
    private readonly campaignRepository: CampaignRepository,
    private readonly userPointsRepository: UserPointsRepository,
    private readonly processedOrderRepository: ProcessedOrderRepository,
  ) {}

  async createCampaign(name: string, totalPoints: number, startDate: Date, endDate: Date): Promise<Campaign> {
    const campaignData = {
      id: uuidv4(),
      name,
      totalPoints,
      remainingPoints: totalPoints,
      startDate,
      endDate,
      status: 'active' as const,
    };

    return await this.campaignRepository.create(campaignData);
  }

  async handleOrderCreated(orderData: { orderId: string; userId: string; timestamp: Date }) {
    // Ensure order hasn't been processed before (idempotency)
    const isProcessed = await this.processedOrderRepository.exists(orderData.orderId);
    if (isProcessed) {
      return;
    }

    const activeCampaign = await this.campaignRepository.findActiveCampaign(orderData.timestamp);

    if (!activeCampaign || activeCampaign.remainingPoints <= 0) {
      return;
    }

    // Default points per order
    const pointsToAward = 10;

    if (activeCampaign.remainingPoints >= pointsToAward) {
      const userPointsData = {
        userId: orderData.userId,
        campaignId: activeCampaign.id,
        points: pointsToAward,
        orderId: orderData.orderId,
        timestamp: orderData.timestamp,
      };

      const userPoints = await this.userPointsRepository.create(userPointsData);

      activeCampaign.remainingPoints -= pointsToAward;
      if (activeCampaign.remainingPoints === 0) {
        activeCampaign.status = 'completed';
      }
      await this.campaignRepository.save(activeCampaign);

      await this.processedOrderRepository.create(orderData.orderId);

      return userPoints;
    }
  }

  async getCampaigns(): Promise<Campaign[]> {
    return await this.campaignRepository.findAll();
  }

  async getUserPoints(userId: string): Promise<UserPoints[]> {
    return await this.userPointsRepository.findByUserId(userId);
  }
}
