import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { Campaign, UserPoints, ProcessedOrder } from './entities';
import { CampaignRepository, UserPointsRepository, ProcessedOrderRepository } from './repositories';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'sqlite',
      database: process.env.NODE_ENV === 'production' ? '/app/campaign.db' : 'campaign.db',
      entities: [Campaign, UserPoints, ProcessedOrder],
      synchronize: true, // Only for development
      logging: true,
    }),
    TypeOrmModule.forFeature([Campaign, UserPoints, ProcessedOrder]),
    ClientsModule.register([
      {
        name: 'KAFKA_SERVICE',
        transport: Transport.KAFKA,
        options: {
          client: {
            clientId: 'campaign',
            brokers: ['localhost:29092', 'localhost:29093', 'localhost:29094'],
          },
          consumer: {
            groupId: 'campaign-consumer',
          },
        },
      },
    ]),
  ],
  controllers: [AppController],
  providers: [AppService, CampaignRepository, UserPointsRepository, ProcessedOrderRepository],
})
export class AppModule {}
