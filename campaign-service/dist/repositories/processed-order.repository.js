"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessedOrderRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const processed_order_entity_1 = require("../entities/processed-order.entity");
let ProcessedOrderRepository = class ProcessedOrderRepository {
    constructor(processedOrderRepo) {
        this.processedOrderRepo = processedOrderRepo;
    }
    async create(orderId) {
        const processedOrder = this.processedOrderRepo.create({
            orderId,
            processedAt: new Date(),
        });
        return await this.processedOrderRepo.save(processedOrder);
    }
    async exists(orderId) {
        const count = await this.processedOrderRepo.count({
            where: { orderId },
        });
        return count > 0;
    }
    async findAll() {
        return await this.processedOrderRepo.find();
    }
};
exports.ProcessedOrderRepository = ProcessedOrderRepository;
exports.ProcessedOrderRepository = ProcessedOrderRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(processed_order_entity_1.ProcessedOrder)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ProcessedOrderRepository);
//# sourceMappingURL=processed-order.repository.js.map