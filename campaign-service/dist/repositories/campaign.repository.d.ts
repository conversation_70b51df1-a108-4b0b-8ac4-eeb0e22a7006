import { Repository } from 'typeorm';
import { Campaign } from '../entities/campaign.entity';
export declare class CampaignRepository {
    private readonly campaignRepo;
    constructor(campaignRepo: Repository<Campaign>);
    create(campaignData: Partial<Campaign>): Promise<Campaign>;
    findAll(): Promise<Campaign[]>;
    findById(id: string): Promise<Campaign | null>;
    findActiveCampaign(timestamp: Date): Promise<Campaign | null>;
    update(id: string, updateData: Partial<Campaign>): Promise<Campaign | null>;
    save(campaign: Campaign): Promise<Campaign>;
}
