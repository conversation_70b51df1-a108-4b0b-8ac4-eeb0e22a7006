"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessedOrderRepository = exports.UserPointsRepository = exports.CampaignRepository = void 0;
var campaign_repository_1 = require("./campaign.repository");
Object.defineProperty(exports, "CampaignRepository", { enumerable: true, get: function () { return campaign_repository_1.CampaignRepository; } });
var user_points_repository_1 = require("./user-points.repository");
Object.defineProperty(exports, "UserPointsRepository", { enumerable: true, get: function () { return user_points_repository_1.UserPointsRepository; } });
var processed_order_repository_1 = require("./processed-order.repository");
Object.defineProperty(exports, "ProcessedOrderRepository", { enumerable: true, get: function () { return processed_order_repository_1.ProcessedOrderRepository; } });
//# sourceMappingURL=index.js.map