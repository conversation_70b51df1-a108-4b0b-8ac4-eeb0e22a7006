import { Repository } from 'typeorm';
import { UserPoints } from '../entities/user-points.entity';
export declare class UserPointsRepository {
    private readonly userPointsRepo;
    constructor(userPointsRepo: Repository<UserPoints>);
    create(userPointsData: Partial<UserPoints>): Promise<UserPoints>;
    findByUserId(userId: string): Promise<UserPoints[]>;
    findByCampaignId(campaignId: string): Promise<UserPoints[]>;
    findAll(): Promise<UserPoints[]>;
}
