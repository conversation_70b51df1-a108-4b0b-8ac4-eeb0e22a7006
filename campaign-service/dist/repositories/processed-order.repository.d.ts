import { Repository } from 'typeorm';
import { ProcessedOrder } from '../entities/processed-order.entity';
export declare class ProcessedOrderRepository {
    private readonly processedOrderRepo;
    constructor(processedOrderRepo: Repository<ProcessedOrder>);
    create(orderId: string): Promise<ProcessedOrder>;
    exists(orderId: string): Promise<boolean>;
    findAll(): Promise<ProcessedOrder[]>;
}
