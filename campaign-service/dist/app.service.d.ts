import { Campaign } from './entities/campaign.entity';
import { UserPoints } from './entities/user-points.entity';
import { CampaignRepository, UserPointsRepository, ProcessedOrderRepository } from './repositories';
export declare class AppService {
    private readonly campaignRepository;
    private readonly userPointsRepository;
    private readonly processedOrderRepository;
    constructor(campaignRepository: CampaignRepository, userPointsRepository: UserPointsRepository, processedOrderRepository: ProcessedOrderRepository);
    createCampaign(name: string, totalPoints: number, startDate: Date, endDate: Date): Promise<Campaign>;
    handleOrderCreated(orderData: {
        orderId: string;
        userId: string;
        timestamp: Date;
    }): Promise<UserPoints>;
    getCampaigns(): Promise<Campaign[]>;
    getUserPoints(userId: string): Promise<UserPoints[]>;
}
