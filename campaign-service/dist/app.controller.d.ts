import { AppService } from './app.service';
import { Campaign } from './entities/campaign.entity';
import { UserPoints } from './entities/user-points.entity';
export declare class AppController {
    private readonly appService;
    constructor(appService: AppService);
    createCampaign(createCampaignDto: {
        name: string;
        totalPoints: number;
        startDate: string;
        endDate: string;
    }): Promise<Campaign>;
    getCampaigns(): Promise<Campaign[]>;
    getUserPoints(userId: string): Promise<UserPoints[]>;
    handleOrderCreated(data: {
        orderId: string;
        userId: string;
        timestamp: string;
    }): Promise<UserPoints>;
}
