"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppService = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
const repositories_1 = require("./repositories");
let AppService = class AppService {
    constructor(campaignRepository, userPointsRepository, processedOrderRepository) {
        this.campaignRepository = campaignRepository;
        this.userPointsRepository = userPointsRepository;
        this.processedOrderRepository = processedOrderRepository;
    }
    async createCampaign(name, totalPoints, startDate, endDate) {
        const campaignData = {
            id: (0, uuid_1.v4)(),
            name,
            totalPoints,
            remainingPoints: totalPoints,
            startDate,
            endDate,
            status: 'active',
        };
        return await this.campaignRepository.create(campaignData);
    }
    async handleOrderCreated(orderData) {
        const isProcessed = await this.processedOrderRepository.exists(orderData.orderId);
        if (isProcessed) {
            return;
        }
        const activeCampaign = await this.campaignRepository.findActiveCampaign(orderData.timestamp);
        if (!activeCampaign || activeCampaign.remainingPoints <= 0) {
            return;
        }
        const pointsToAward = 10;
        if (activeCampaign.remainingPoints >= pointsToAward) {
            const userPointsData = {
                userId: orderData.userId,
                campaignId: activeCampaign.id,
                points: pointsToAward,
                orderId: orderData.orderId,
                timestamp: orderData.timestamp,
            };
            const userPoints = await this.userPointsRepository.create(userPointsData);
            activeCampaign.remainingPoints -= pointsToAward;
            if (activeCampaign.remainingPoints === 0) {
                activeCampaign.status = 'completed';
            }
            await this.campaignRepository.save(activeCampaign);
            await this.processedOrderRepository.create(orderData.orderId);
            return userPoints;
        }
    }
    async getCampaigns() {
        return await this.campaignRepository.findAll();
    }
    async getUserPoints(userId) {
        return await this.userPointsRepository.findByUserId(userId);
    }
};
exports.AppService = AppService;
exports.AppService = AppService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.CampaignRepository,
        repositories_1.UserPointsRepository,
        repositories_1.ProcessedOrderRepository])
], AppService);
//# sourceMappingURL=app.service.js.map