#!/bin/bash

echo "=== Testing Order Service with Localhost Kafka ==="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ $2${NC}"
    else
        echo -e "${RED}✗ $2${NC}"
    fi
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 1. Check Kafka on localhost
echo -e "${YELLOW}1. Checking Kafka on localhost...${NC}"
KAFKA_ACCESSIBLE=0
for port in 29092 29093 29094; do
    if nc -z localhost $port 2>/dev/null; then
        print_status 0 "localhost:$port accessible"
        KAFKA_ACCESSIBLE=1
    else
        print_status 1 "localhost:$port not accessible"
    fi
done

if [ $KAFKA_ACCESSIBLE -eq 0 ]; then
    echo -e "${RED}No Kafka brokers accessible on localhost. Please start Kafka first.${NC}"
    exit 1
fi

echo ""

# 2. Set development environment
echo -e "${YELLOW}2. Setting development environment...${NC}"
export NODE_ENV=development
print_info "NODE_ENV set to: $NODE_ENV"

# 3. Kill any existing processes
echo -e "${YELLOW}3. Cleaning up existing processes...${NC}"
pkill -f "npm run start:dev" 2>/dev/null || true
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
lsof -ti:3001 | xargs kill -9 2>/dev/null || true
sleep 2

# 4. Start Order Service
echo -e "${YELLOW}4. Starting Order Service...${NC}"
cd order-service
NODE_ENV=development npm run start:dev &
ORDER_PID=$!
print_info "Order Service started with PID: $ORDER_PID"
cd ..

# Wait for service to start
print_info "Waiting for Order Service to start..."
sleep 10

# Check if Order Service is running
ORDER_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/courses 2>/dev/null || echo "000")
if [ "$ORDER_HEALTH" = "200" ]; then
    print_status 0 "Order Service is running and healthy"
else
    print_status 1 "Order Service failed to start (HTTP $ORDER_HEALTH)"
    kill $ORDER_PID 2>/dev/null
    exit 1
fi

# 5. Start Campaign Service
echo -e "${YELLOW}5. Starting Campaign Service...${NC}"
cd campaign-service
NODE_ENV=development npm run start:dev &
CAMPAIGN_PID=$!
print_info "Campaign Service started with PID: $CAMPAIGN_PID"
cd ..

# Wait for service to start
print_info "Waiting for Campaign Service to start..."
sleep 10

# Check if Campaign Service is running
CAMPAIGN_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/campaigns 2>/dev/null || echo "000")
if [ "$CAMPAIGN_HEALTH" = "200" ]; then
    print_status 0 "Campaign Service is running and healthy"
else
    print_status 1 "Campaign Service failed to start (HTTP $CAMPAIGN_HEALTH)"
    kill $ORDER_PID $CAMPAIGN_PID 2>/dev/null
    exit 1
fi

echo ""

# 6. Test Kafka message flow
echo -e "${YELLOW}6. Testing Kafka message flow...${NC}"

print_info "Getting available courses..."
COURSES_RESPONSE=$(curl -s http://localhost:3000/courses 2>/dev/null)
print_info "Available courses: $COURSES_RESPONSE"

print_info "Creating an order..."
ORDER_RESPONSE=$(curl -s -X POST http://localhost:3000/orders \
  -H "Content-Type: application/json" \
  -d '{"userId": "localhost-test-user", "courseId": "1"}' 2>/dev/null)

if echo "$ORDER_RESPONSE" | grep -q '"id"'; then
    print_status 0 "Order created successfully"
    echo "Order: $ORDER_RESPONSE"
    
    # Wait for Kafka message processing
    print_info "Waiting for Kafka message processing..."
    sleep 5
    
    # Check if user got points
    POINTS_RESPONSE=$(curl -s http://localhost:3001/points/localhost-test-user 2>/dev/null)
    
    if echo "$POINTS_RESPONSE" | grep -q '"points"'; then
        print_status 0 "Kafka message processed successfully!"
        echo "User points: $POINTS_RESPONSE"
        echo -e "${GREEN}🎉 Localhost Kafka integration is working!${NC}"
    else
        print_status 1 "Kafka message processing failed"
        echo "Points response: $POINTS_RESPONSE"
    fi
else
    print_status 1 "Failed to create order"
    echo "Order response: $ORDER_RESPONSE"
fi

echo ""

# 7. Performance test
echo -e "${YELLOW}7. Quick performance test...${NC}"

SUCCESS_COUNT=0
TOTAL_ORDERS=3

for i in $(seq 1 $TOTAL_ORDERS); do
    ORDER_RESPONSE=$(curl -s -X POST http://localhost:3000/orders \
      -H "Content-Type: application/json" \
      -d "{\"userId\": \"perf-user-$i\", \"courseId\": \"1\"}" 2>/dev/null)
    
    if echo "$ORDER_RESPONSE" | grep -q '"id"'; then
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    fi
    sleep 1
done

print_status 0 "Created $SUCCESS_COUNT/$TOTAL_ORDERS orders"

# Wait for processing
sleep 5

# Check processed orders
PROCESSED_COUNT=0
for i in $(seq 1 $TOTAL_ORDERS); do
    POINTS_RESPONSE=$(curl -s http://localhost:3001/points/perf-user-$i 2>/dev/null)
    if echo "$POINTS_RESPONSE" | grep -q '"points"'; then
        PROCESSED_COUNT=$((PROCESSED_COUNT + 1))
    fi
done

print_status 0 "Processed $PROCESSED_COUNT/$SUCCESS_COUNT orders via Kafka"

echo ""

# Summary
echo -e "${YELLOW}=== Test Summary ===${NC}"
echo -e "${GREEN}✓ Environment: Development (localhost)${NC}"
echo -e "${GREEN}✓ Kafka brokers: localhost:29092,29093,29094${NC}"
echo -e "${GREEN}✓ Order Service: Running on localhost:3000${NC}"
echo -e "${GREEN}✓ Campaign Service: Running on localhost:3001${NC}"

if [ $PROCESSED_COUNT -eq $SUCCESS_COUNT ] && [ $SUCCESS_COUNT -gt 0 ]; then
    echo -e "${GREEN}✓ Kafka Integration: Working perfectly!${NC}"
else
    echo -e "${RED}⚠ Kafka Integration: Some issues detected${NC}"
fi

echo ""
print_info "Services are running in the background."
print_info "Order Service PID: $ORDER_PID"
print_info "Campaign Service PID: $CAMPAIGN_PID"
print_info "To stop services, run: kill $ORDER_PID $CAMPAIGN_PID"

echo ""
print_info "Test endpoints:"
echo "  Order Service: http://localhost:3000"
echo "  Campaign Service: http://localhost:3001"
echo "  Create order: POST http://localhost:3000/orders"
echo "  Check points: GET http://localhost:3001/points/{userId}"
